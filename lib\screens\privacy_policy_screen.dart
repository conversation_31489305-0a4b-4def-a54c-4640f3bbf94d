import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../generated/l10n/app_localizations.dart';

class PrivacyPolicyScreen extends StatelessWidget {
  const PrivacyPolicyScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF7F9FC),
      appBar: AppBar(
        backgroundColor: const Color(0xFFF7F9FC),
        elevation: 0,
        leading: IconButton(
          onPressed: () => Navigator.of(context).pop(),
          icon: const Icon(
            Icons.arrow_back_ios,
            color: Color(0xFF2D3748),
          ),
        ),
        title: Text(
          AppLocalizations.of(context).privacyPolicy,
          style: GoogleFonts.instrumentSans(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: const Color(0xFF2D3748),
          ),
        ),
        centerTitle: true,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildLastUpdated(),
            const SizedBox(height: 24),
            _buildSection(
              'Introduction',
              'TripwiseGO ("we," "our," or "us") respects your privacy and is committed to protecting your personal information. This Privacy Policy explains how we collect, use, disclose, and safeguard your information when you use our mobile application.',
            ),
            _buildSection(
              'Information We Collect',
              'We collect information you provide directly to us:\n\n• Account information (name, email, profile picture)\n• Travel preferences and survey responses\n• Itineraries and trip plans you create\n• Messages and content you share\n• Feedback and support communications\n\nWe automatically collect:\n\n• Device information (device type, operating system)\n• Usage data (features used, time spent)\n• Location data (when you grant permission)\n• Log data (IP address, access times)',
            ),
            _buildSection(
              'Authentication Services',
              'We use Supabase for authentication and Google OAuth for sign-in services. When you sign in with Google, we receive basic profile information (name, email, profile picture) as permitted by your Google account settings.',
            ),
            _buildSection(
              'AI and Chat Features',
              'Our AI-powered chat feature processes your messages to provide travel recommendations. Chat conversations are stored to improve our services and provide personalized experiences. We use Google Gemini API for AI processing.',
            ),
            _buildSection(
              'Location Services',
              'With your permission, we collect location data to:\n\n• Provide location-based recommendations\n• Enhance itinerary planning\n• Improve our services\n\nYou can disable location access in your device settings at any time.',
            ),
            _buildSection(
              'Collaborative Features',
              'When you share itineraries or collaborate with others, the shared content becomes visible to invited participants. You control who can access your collaborative itineraries.',
            ),
            _buildSection(
              'How We Use Your Information',
              'We use your information to:\n\n• Provide and improve our services\n• Create personalized travel recommendations\n• Enable collaborative features\n• Communicate with you about your account\n• Respond to support requests\n• Analyze usage patterns to improve the app\n• Ensure security and prevent fraud',
            ),
            _buildSection(
              'Data Sharing',
              'We do not sell your personal information. We may share information:\n\n• With service providers (Supabase, Google, cloud storage)\n• When required by law or legal process\n• To protect our rights or safety\n• With your consent for specific purposes\n• In anonymized form for analytics',
            ),
            _buildSection(
              'Data Storage and Security',
              'Your data is stored securely using Supabase infrastructure with industry-standard encryption. We implement appropriate technical and organizational measures to protect your information against unauthorized access, alteration, disclosure, or destruction.\n\nHowever, no method of transmission over the internet is 100% secure, and we cannot guarantee absolute security.',
            ),
            _buildSection(
              'Data Retention',
              'We retain your information for as long as necessary to provide our services and fulfill the purposes outlined in this policy. You may request deletion of your account and associated data at any time.',
            ),
            _buildSection(
              'Your Rights and Choices',
              'You have the right to:\n\n• Access your personal information\n• Correct inaccurate information\n• Delete your account and data\n• Export your data\n• Opt out of certain communications\n• Control location sharing\n• Manage collaborative sharing settings\n\nTo exercise these rights, contact <NAME_EMAIL>.',
            ),
            _buildSection(
              'Children\'s Privacy',
              'Our service is not intended for children under 13. We do not knowingly collect personal information from children under 13. If you believe we have collected information from a child under 13, please contact us immediately.',
            ),
            _buildSection(
              'International Data Transfers',
              'Your information may be transferred to and processed in countries other than your own. We ensure appropriate safeguards are in place to protect your information in accordance with this Privacy Policy.',
            ),
            _buildSection(
              'Changes to This Policy',
              'We may update this Privacy Policy from time to time. We will notify you of any material changes by posting the new policy in the app and updating the "Last Updated" date. Your continued use constitutes acceptance of the updated policy.',
            ),
            _buildSection(
              'Contact Us',
              'If you have questions about this Privacy Policy or our data practices, please contact us:\n\nEmail: <EMAIL>\nAddress: [Your Company Address]\n\nFor data protection inquiries in the EU, contact our Data Protection <NAME_EMAIL>.',
            ),
            const SizedBox(height: 40),
          ],
        ),
      ),
    );
  }

  Widget _buildLastUpdated() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xFF10B981).withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: const Color(0xFF10B981).withOpacity(0.2),
        ),
      ),
      child: Row(
        children: [
          const Icon(
            Icons.privacy_tip_outlined,
            color: Color(0xFF10B981),
            size: 20,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              'Last updated: July 4, 2025',
              style: GoogleFonts.instrumentSans(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: const Color(0xFF10B981),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSection(String title, String content) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: GoogleFonts.instrumentSans(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: const Color(0xFF2D3748),
            ),
          ),
          const SizedBox(height: 12),
          Text(
            content,
            style: GoogleFonts.instrumentSans(
              fontSize: 14,
              height: 1.6,
              color: const Color(0xFF4A5568),
            ),
          ),
        ],
      ),
    );
  }
}
