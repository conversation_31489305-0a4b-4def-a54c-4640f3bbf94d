import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:google_fonts/google_fonts.dart';
import '../models/collaborative_itinerary.dart';
import '../models/itinerary_comment.dart';
import '../services/collaborative_itinerary_service.dart';
import '../services/auth_service.dart';
import '../services/image_upload_service.dart';
import '../config/supabase_config.dart';
import '../widgets/image_upload_widget.dart';
import 'homepage_screen.dart';

class CollaborativeItineraryDetailScreen extends StatefulWidget {
  final CollaborativeItinerary itinerary;

  const CollaborativeItineraryDetailScreen({
    super.key,
    required this.itinerary,
  });

  @override
  State<CollaborativeItineraryDetailScreen> createState() =>
      _CollaborativeItineraryDetailScreenState();
}

class _CollaborativeItineraryDetailScreenState
    extends State<CollaborativeItineraryDetailScreen>
    with TickerProviderStateMixin {
  late AnimationController _fadeController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  // Navigation animation controllers
  late AnimationController _navAnimationController;
  late List<AnimationController> _navItemControllers;
  late List<Animation<double>> _navItemScaleAnimations;
  late List<Animation<double>> _navItemFadeAnimations;

  late CollaborativeItinerary _currentItinerary;
  bool _isLoading = false;

  // Bottom navigation state
  int _currentTabIndex = 0;
  late PageController _pageController;

  // Comments data
  List<ItineraryComment> _comments = [];
  bool _isLoadingComments = false;
  final TextEditingController _commentController = TextEditingController();

  // Participants data
  List<CollaborationParticipant> _participants = [];
  bool _isLoadingParticipants = false;

  // Real-time subscriptions
  StreamSubscription<CollaborativeItinerary>? _itinerarySubscription;
  StreamSubscription<List<ItineraryComment>>? _commentsSubscription;

  // Periodic check for itinerary existence
  Timer? _existenceCheckTimer;

  // Timeline state variables
  int _selectedDayIndex = 0;
  bool _isDragging = false;
  String? _draggedActivityKey;

  // Delete functionality state
  bool _isDeleting = false;

  @override
  void initState() {
    super.initState();
    _currentItinerary = widget.itinerary;
    _pageController = PageController();

    // Initialize animations
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeInOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(1.0, 0.0),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeInOut,
    ));

    // Initialize navigation animations
    _navAnimationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    // Initialize individual nav item controllers with reduced duration
    _navItemControllers = List.generate(
        3,
        (index) => AnimationController(
              duration: const Duration(milliseconds: 150), // Reduced from 300ms
              vsync: this,
            ));

    // Initialize simplified scale animations for nav items
    _navItemScaleAnimations = _navItemControllers
        .map((controller) => Tween<double>(
              begin: 1.0,
              end: 1.02, // Reduced from 1.05 for subtler effect
            ).animate(CurvedAnimation(
              parent: controller,
              curve: Curves.easeInOut, // Simplified from Curves.elasticOut
            )))
        .toList();

    // Initialize simplified fade animations for nav items
    _navItemFadeAnimations = _navItemControllers
        .map((controller) => Tween<double>(
              begin: 0.8, // Increased from 0.7 for subtler effect
              end: 1.0,
            ).animate(CurvedAnimation(
              parent: controller,
              curve: Curves.easeInOut,
            )))
        .toList();

    // Start initial animations with staggered timing
    _fadeController.forward();

    // Stagger the navigation animations for a more polished entrance
    Future.delayed(const Duration(milliseconds: 200), () {
      if (mounted) {
        _navAnimationController.forward();
      }
    });

    Future.delayed(const Duration(milliseconds: 300), () {
      if (mounted) {
        _navItemControllers[_currentTabIndex].forward();
      }
    });

    // Load initial data
    _loadComments();
    _loadParticipants();
    _subscribeToUpdates();
    _startExistenceCheck();
  }

  @override
  void dispose() {
    _fadeController.dispose();
    _navAnimationController.dispose();
    for (final controller in _navItemControllers) {
      controller.dispose();
    }
    _pageController.dispose();
    _commentController.dispose();
    _itinerarySubscription?.cancel();
    _commentsSubscription?.cancel();
    _existenceCheckTimer?.cancel();
    CollaborativeItineraryService.unsubscribeAll();
    super.dispose();
  }

  void _subscribeToUpdates() {
    // Subscribe to itinerary updates
    CollaborativeItineraryService.subscribeToItinerary(_currentItinerary.id);
    _itinerarySubscription = CollaborativeItineraryService.itineraryUpdates
        .listen((updatedItinerary) {
      if (mounted) {
        setState(() {
          _currentItinerary = updatedItinerary;
        });
      }
    }, onError: (error) {
      if (kDebugMode) {
        print('Error in itinerary subscription: $error');
      }

      // Handle case where itinerary was deleted
      if (error.toString().contains('not found') ||
          error.toString().contains('deleted') ||
          error.toString().contains('404') ||
          error.toString().contains('null') ||
          error.toString().contains('does not exist')) {
        _handleItineraryDeleted();
      }
    });

    // Subscribe to comments updates
    CollaborativeItineraryService.subscribeToComments(_currentItinerary.id);
    _commentsSubscription =
        CollaborativeItineraryService.commentsUpdates.listen((updatedComments) {
      if (mounted) {
        setState(() {
          _comments = updatedComments;
        });
        if (kDebugMode) {
          print(
              'Comments updated via real-time: ${updatedComments.length} comments');
        }
      }
    }, onError: (error) {
      if (kDebugMode) {
        print('Error in comments subscription: $error');
      }
      // Check if error is due to itinerary deletion
      if (error.toString().contains('not found') ||
          error.toString().contains('deleted') ||
          error.toString().contains('404') ||
          error.toString().contains('null') ||
          error.toString().contains('does not exist')) {
        _handleItineraryDeleted();
      } else {
        // Fallback: reload comments manually if real-time fails for other reasons
        _loadComments();
      }
    });
  }

  /// Start periodic check to verify itinerary still exists
  void _startExistenceCheck() {
    _existenceCheckTimer = Timer.periodic(
      const Duration(seconds: 30), // Check every 30 seconds
      (timer) async {
        if (!mounted) {
          timer.cancel();
          return;
        }

        try {
          // Try to fetch the itinerary to check if it still exists
          final response = await SupabaseConfig.client
              .from('collaborative_itineraries')
              .select('id')
              .eq('id', _currentItinerary.id)
              .maybeSingle();

          if (response == null && mounted) {
            // Itinerary no longer exists
            timer.cancel();
            _handleItineraryDeleted();
          }
        } catch (e) {
          if (kDebugMode) {
            print('Error checking itinerary existence: $e');
          }
          // If error suggests itinerary doesn't exist, handle deletion
          if (e.toString().contains('not found') ||
              e.toString().contains('404') ||
              e.toString().contains('null')) {
            timer.cancel();
            _handleItineraryDeleted();
          }
        }
      },
    );
  }

  Future<void> _loadComments() async {
    setState(() {
      _isLoadingComments = true;
    });

    try {
      final comments =
          await CollaborativeItineraryService.getComments(_currentItinerary.id);
      setState(() {
        _comments = comments;
        _isLoadingComments = false;
      });
    } catch (e) {
      setState(() {
        _isLoadingComments = false;
      });
      if (kDebugMode) {
        print('Error loading comments: $e');
      }
    }
  }

  Future<void> _loadParticipants() async {
    setState(() {
      _isLoadingParticipants = true;
    });

    try {
      final participants = await CollaborativeItineraryService.getParticipants(
          _currentItinerary.id);
      setState(() {
        _participants = participants;
        _isLoadingParticipants = false;
      });
    } catch (e) {
      setState(() {
        _isLoadingParticipants = false;
      });
      if (kDebugMode) {
        print('Error loading participants: $e');
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF7F9FC),
      appBar: AppBar(
        backgroundColor: const Color(0xFFF7F9FC),
        surfaceTintColor: const Color(0xFFF7F9FC),
        shadowColor: Colors.transparent,
        elevation: 0,
        scrolledUnderElevation: 0,
        leading: IconButton(
          onPressed: () => Navigator.of(context).pop(),
          icon: const Icon(
            Icons.arrow_back_ios,
            color: Color(0xFF2D3748),
          ),
        ),
        title: Column(
          children: [
            Text(
              _currentItinerary.title,
              style: GoogleFonts.instrumentSans(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: const Color(0xFF2D3748),
              ),
            ),
            Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Icon(
                  Icons.people,
                  size: 12,
                  color: Color(0xFF0D76FF),
                ),
                const SizedBox(width: 4),
                Text(
                  'Collaborative',
                  style: GoogleFonts.instrumentSans(
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                    color: const Color(0xFF0D76FF),
                  ),
                ),
              ],
            ),
          ],
        ),
        centerTitle: true,
      ),
      body: FadeTransition(
        opacity: _fadeAnimation,
        child: SlideTransition(
          position: _slideAnimation,
          child: _isLoading
              ? const Center(
                  child: CircularProgressIndicator(
                    color: Color(0xFF0D76FF),
                  ),
                )
              : Column(
                  children: [
                    Expanded(
                      child: PageView(
                        controller: _pageController,
                        physics: const BouncingScrollPhysics(),
                        onPageChanged: (index) {
                          _onTabChanged(index);
                        },
                        children: [
                          _buildAnimatedTabContent(_buildItineraryTab(), 0),
                          _buildAnimatedTabContent(_buildCommentsTab(), 1),
                          _buildAnimatedTabContent(_buildShareTab(), 2),
                        ],
                      ),
                    ),
                    _buildCircularBottomNavigation(),
                  ],
                ),
        ),
      ),
    );
  }

  Widget _buildItineraryTab() {
    return Column(
      children: [
        // Trip info card
        Container(
          width: double.infinity,
          margin: const EdgeInsets.all(16),
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.05),
                blurRadius: 10,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  const Icon(
                    Icons.calendar_today,
                    color: Color(0xFF0D76FF),
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    _currentItinerary.dateRange,
                    style: GoogleFonts.instrumentSans(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: const Color(0xFF2D3748),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              Row(
                children: [
                  const Icon(
                    Icons.location_on,
                    color: Color(0xFF0D76FF),
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      _currentItinerary.destinations.join(', '),
                      style: GoogleFonts.instrumentSans(
                        fontSize: 14,
                        color: const Color(0xFF718096),
                      ),
                    ),
                  ),
                ],
              ),
              if (_currentItinerary.accommodation.isNotEmpty) ...[
                const SizedBox(height: 12),
                Row(
                  children: [
                    const Icon(
                      Icons.hotel,
                      color: Color(0xFF0D76FF),
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        _currentItinerary.accommodation,
                        style: GoogleFonts.instrumentSans(
                          fontSize: 14,
                          color: const Color(0xFF718096),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ],
          ),
        ),
        const SizedBox(height: 12),
        // Activities section
        Expanded(
          child: _buildActivitiesSection(),
        ),
      ],
    );
  }

  // Tab change handling with animations
  void _onTabChanged(int index) {
    if (_currentTabIndex != index) {
      // Animate out the current tab
      _navItemControllers[_currentTabIndex].reverse();

      setState(() {
        _currentTabIndex = index;
      });

      // Animate in the new tab
      _navItemControllers[index].forward();
    }
  }

  Widget _buildAnimatedTabContent(Widget child, int tabIndex) {
    return AnimatedBuilder(
      animation: _navItemControllers[tabIndex],
      builder: (context, _) {
        return FadeTransition(
          opacity: _navItemFadeAnimations[tabIndex],
          child: Transform.translate(
            offset: Offset(
              0,
              (1 - _navItemFadeAnimations[tabIndex].value) * 20,
            ),
            child: child,
          ),
        );
      },
    );
  }

  Widget _buildActivitiesSection() {
    // Check if we have activities to display
    final hasActivities = _currentItinerary.daySpecificActivities != null &&
        _currentItinerary.daySpecificActivities!.isNotEmpty;

    if (!hasActivities) {
      return _buildEmptyActivitiesState();
    }

    return Column(
      children: [
        // Day selector
        _buildDaySelector(),
        const SizedBox(height: 16),
        // Timeline view
        Expanded(
          child: _buildTimelineView(),
        ),
      ],
    );
  }

  Widget _buildEmptyActivitiesState() {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.event_note,
            size: 48,
            color: const Color(0xFF718096).withOpacity(0.5),
          ),
          const SizedBox(height: 16),
          Text(
            'No Activities Yet',
            style: GoogleFonts.instrumentSans(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: const Color(0xFF718096),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Activities will appear here once they are added to the itinerary',
            style: GoogleFonts.instrumentSans(
              fontSize: 14,
              color: const Color(0xFF718096),
              height: 1.4,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildDaySelector() {
    final totalDays = _currentItinerary.totalDays;
    if (totalDays <= 1) return const SizedBox.shrink();

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Select Day',
            style: GoogleFonts.instrumentSans(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: const Color(0xFF2D3748),
            ),
          ),
          const SizedBox(height: 12),
          SizedBox(
            height: 60,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: totalDays,
              itemBuilder: (context, index) {
                final isSelected = _selectedDayIndex == index;
                final day = index + 1;

                // Calculate the actual date for this day
                DateTime? dayDate;
                try {
                  final startDate =
                      _parseDisplayDate(_currentItinerary.startDate);
                  dayDate = startDate.add(Duration(days: index));
                } catch (e) {
                  // Fallback if date parsing fails
                }

                return GestureDetector(
                  onTap: () {
                    setState(() {
                      _selectedDayIndex = index;
                    });
                  },
                  child: Container(
                    width: 60,
                    margin: const EdgeInsets.only(right: 12),
                    decoration: BoxDecoration(
                      color:
                          isSelected ? const Color(0xFF0D76FF) : Colors.white,
                      borderRadius: BorderRadius.circular(16),
                      border: Border.all(
                        color: isSelected
                            ? const Color(0xFF0D76FF)
                            : const Color(0xFFE2E8F0),
                        width: 1,
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.05),
                          blurRadius: 10,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          'Day',
                          style: GoogleFonts.instrumentSans(
                            fontSize: 10,
                            fontWeight: FontWeight.w500,
                            color: isSelected
                                ? Colors.white
                                : const Color(0xFF718096),
                          ),
                        ),
                        Text(
                          '$day',
                          style: GoogleFonts.instrumentSans(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: isSelected
                                ? Colors.white
                                : const Color(0xFF2D3748),
                          ),
                        ),
                        if (dayDate != null)
                          Text(
                            _getMonthName(dayDate),
                            style: GoogleFonts.instrumentSans(
                              fontSize: 10,
                              fontWeight: FontWeight.w500,
                              color: isSelected
                                  ? Colors.white
                                  : const Color(0xFF718096),
                            ),
                          ),
                      ],
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTimelineView() {
    return SingleChildScrollView(
      physics: const BouncingScrollPhysics(),
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 16),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildTimeAxis(),
            const SizedBox(width: 16),
            Expanded(
              child: _buildActivitiesColumn(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildParticipantsList() {
    if (_isLoadingParticipants) {
      return const Center(
        child: CircularProgressIndicator(
          color: Color(0xFF0D76FF),
        ),
      );
    }

    if (_participants.isEmpty) {
      return Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 10,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Text(
          'No participants yet',
          style: GoogleFonts.instrumentSans(
            fontSize: 14,
            color: const Color(0xFF718096),
          ),
        ),
      );
    }

    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: _participants.map((participant) {
          return Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              border: Border(
                bottom: BorderSide(
                  color: const Color(0xFFE2E8F0),
                  width: _participants.last == participant ? 0 : 1,
                ),
              ),
            ),
            child: Row(
              children: [
                CircleAvatar(
                  radius: 20,
                  backgroundColor: const Color(0xFF0D76FF).withOpacity(0.1),
                  child: Text(
                    participant.initials,
                    style: GoogleFonts.instrumentSans(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      color: const Color(0xFF0D76FF),
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        participant.displayName,
                        style: GoogleFonts.instrumentSans(
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                          color: const Color(0xFF2D3748),
                        ),
                      ),
                      Text(
                        participant.roleDisplayName,
                        style: GoogleFonts.instrumentSans(
                          fontSize: 12,
                          color: const Color(0xFF718096),
                        ),
                      ),
                    ],
                  ),
                ),
                if (participant.isOwner)
                  Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: const Color(0xFF10B981).withOpacity(0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      'Owner',
                      style: GoogleFonts.instrumentSans(
                        fontSize: 10,
                        fontWeight: FontWeight.w600,
                        color: const Color(0xFF10B981),
                      ),
                    ),
                  ),
              ],
            ),
          );
        }).toList(),
      ),
    );
  }

  Widget _buildCommentsTab() {
    return Column(
      children: [
        Expanded(
          child: _isLoadingComments
              ? const Center(
                  child: CircularProgressIndicator(
                    color: Color(0xFF0D76FF),
                  ),
                )
              : _comments.isEmpty
                  ? _buildEmptyCommentsState()
                  : _buildCommentsList(),
        ),
        _buildCommentInput(),
      ],
    );
  }

  Widget _buildEmptyCommentsState() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                color: const Color(0xFF0D76FF).withOpacity(0.1),
                shape: BoxShape.circle,
              ),
              child: const Icon(
                Icons.chat_bubble_outline,
                size: 40,
                color: Color(0xFF0D76FF),
              ),
            ),
            const SizedBox(height: 24),
            Text(
              'No comments yet',
              style: GoogleFonts.instrumentSans(
                fontSize: 20,
                fontWeight: FontWeight.w600,
                color: const Color(0xFF2D3748),
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Start the conversation by adding a comment',
              style: GoogleFonts.instrumentSans(
                fontSize: 14,
                color: const Color(0xFF718096),
                height: 1.4,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCommentsList() {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _comments.length,
      itemBuilder: (context, index) {
        final comment = _comments[index];
        final currentUserId = SupabaseConfig.client.auth.currentUser?.id;
        final isOwnComment = comment.isOwnComment(currentUserId);

        return TweenAnimationBuilder<double>(
          duration: const Duration(milliseconds: 300),
          tween: Tween(begin: 0.0, end: 1.0),
          curve: Curves.easeInOut,
          builder: (context, value, child) {
            return Transform.translate(
              offset: Offset(0, 20 * (1 - value)),
              child: Opacity(
                opacity: value,
                child: child,
              ),
            );
          },
          child: Container(
            margin: const EdgeInsets.only(bottom: 16),
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: isOwnComment
                  ? const Color(0xFF0D76FF).withOpacity(0.05)
                  : Colors.white,
              borderRadius: BorderRadius.circular(16),
              border: isOwnComment
                  ? Border.all(
                      color: const Color(0xFF0D76FF).withOpacity(0.2), width: 1)
                  : null,
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.05),
                  blurRadius: 10,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    CircleAvatar(
                      radius: 16,
                      backgroundColor: isOwnComment
                          ? const Color(0xFF0D76FF).withOpacity(0.2)
                          : const Color(0xFF0D76FF).withOpacity(0.1),
                      child: Text(
                        comment.initials,
                        style: GoogleFonts.instrumentSans(
                          fontSize: 12,
                          fontWeight: FontWeight.w600,
                          color: const Color(0xFF0D76FF),
                        ),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Text(
                                comment.displayName,
                                style: GoogleFonts.instrumentSans(
                                  fontSize: 14,
                                  fontWeight: FontWeight.w600,
                                  color: const Color(0xFF2D3748),
                                ),
                              ),
                              if (isOwnComment) ...[
                                const SizedBox(width: 8),
                                Container(
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 6,
                                    vertical: 2,
                                  ),
                                  decoration: BoxDecoration(
                                    color: const Color(0xFF0D76FF),
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  child: Text(
                                    'You',
                                    style: GoogleFonts.instrumentSans(
                                      fontSize: 10,
                                      fontWeight: FontWeight.w600,
                                      color: Colors.white,
                                    ),
                                  ),
                                ),
                              ],
                            ],
                          ),
                          Text(
                            comment.timeAgo,
                            style: GoogleFonts.instrumentSans(
                              fontSize: 12,
                              color: const Color(0xFF718096),
                            ),
                          ),
                        ],
                      ),
                    ),
                    if (isOwnComment)
                      PopupMenuButton<String>(
                        icon: const Icon(
                          Icons.more_vert,
                          color: Color(0xFF718096),
                          size: 18,
                        ),
                        onSelected: (value) {
                          if (value == 'delete') {
                            _showDeleteCommentDialog(comment);
                          }
                        },
                        itemBuilder: (context) => [
                          PopupMenuItem<String>(
                            value: 'delete',
                            child: Row(
                              children: [
                                const Icon(
                                  Icons.delete_outline,
                                  color: Colors.red,
                                  size: 18,
                                ),
                                const SizedBox(width: 8),
                                Text(
                                  'Delete',
                                  style: GoogleFonts.instrumentSans(
                                    color: Colors.red,
                                    fontSize: 14,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                  ],
                ),
                const SizedBox(height: 12),
                Text(
                  comment.content,
                  style: GoogleFonts.instrumentSans(
                    fontSize: 14,
                    color: const Color(0xFF2D3748),
                    height: 1.4,
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildCommentInput() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: const BoxDecoration(
        color: Colors.white,
        border: Border(
          top: BorderSide(
            color: Color(0xFFE2E8F0),
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: TextField(
              controller: _commentController,
              decoration: InputDecoration(
                hintText: 'Add a comment...',
                hintStyle: GoogleFonts.instrumentSans(
                  color: const Color(0xFF718096),
                ),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(24),
                  borderSide: const BorderSide(color: Color(0xFFE2E8F0)),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(24),
                  borderSide:
                      const BorderSide(color: Color(0xFF0D76FF), width: 2),
                ),
                contentPadding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              ),
              style: GoogleFonts.instrumentSans(
                fontSize: 14,
                color: const Color(0xFF2D3748),
              ),
              maxLines: null,
              textCapitalization: TextCapitalization.sentences,
            ),
          ),
          const SizedBox(width: 12),
          GestureDetector(
            onTap: _addComment,
            child: Container(
              width: 48,
              height: 48,
              decoration: const BoxDecoration(
                color: Color(0xFF0D76FF),
                shape: BoxShape.circle,
              ),
              child: const Icon(
                Icons.send,
                color: Colors.white,
                size: 20,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildShareTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Collaboration code section
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.05),
                  blurRadius: 10,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              children: [
                Container(
                  width: 60,
                  height: 60,
                  decoration: BoxDecoration(
                    color: const Color(0xFF0D76FF).withOpacity(0.1),
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(
                    Icons.qr_code,
                    size: 30,
                    color: Color(0xFF0D76FF),
                  ),
                ),
                const SizedBox(height: 16),
                Text(
                  'Trip Code',
                  style: GoogleFonts.instrumentSans(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    color: const Color(0xFF2D3748),
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'Share this code with others to let them join your trip',
                  style: GoogleFonts.instrumentSans(
                    fontSize: 14,
                    color: const Color(0xFF718096),
                    height: 1.4,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 20),
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: const Color(0xFF0D76FF).withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: const Color(0xFF0D76FF).withOpacity(0.3),
                      width: 1,
                    ),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        _currentItinerary.collaborationCode,
                        style: GoogleFonts.instrumentSans(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                          color: const Color(0xFF0D76FF),
                          letterSpacing: 2,
                        ),
                      ),
                      IconButton(
                        onPressed: () {
                          Clipboard.setData(ClipboardData(
                              text: _currentItinerary.collaborationCode));
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Text(
                                'Code copied to clipboard!',
                                style: GoogleFonts.instrumentSans(
                                    color: Colors.white),
                              ),
                              backgroundColor: const Color(0xFF10B981),
                              behavior: SnackBarBehavior.floating,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(10),
                              ),
                            ),
                          );
                        },
                        icon: const Icon(
                          Icons.copy,
                          color: Color(0xFF0D76FF),
                        ),
                        tooltip: 'Copy code',
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 20),
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: _shareCode,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFF0D76FF),
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      elevation: 0,
                    ),
                    child: Text(
                      'Share your trip',
                      style: GoogleFonts.instrumentSans(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 24),

          // Participants section
          Text(
            'Participants (${_participants.length})',
            style: GoogleFonts.instrumentSans(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: const Color(0xFF2D3748),
            ),
          ),
          const SizedBox(height: 12),
          _buildParticipantsList(),
          const SizedBox(height: 24),

          // Owner-only delete section
          if (_isCurrentUserOwner) ...[
            _buildDeleteSection(),
            const SizedBox(height: 24),
          ],

          // Trip stats
          Text(
            'Trip Statistics',
            style: GoogleFonts.instrumentSans(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: const Color(0xFF2D3748),
            ),
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  icon: Icons.people,
                  title: 'Participants',
                  value: '${_participants.length}',
                  color: const Color(0xFF0D76FF),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildStatCard(
                  icon: Icons.chat_bubble,
                  title: 'Comments',
                  value: '${_comments.length}',
                  color: const Color(0xFF10B981),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  icon: Icons.location_on,
                  title: 'Destinations',
                  value: '${_currentItinerary.destinations.length}',
                  color: const Color(0xFFF59E0B),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildStatCard(
                  icon: Icons.calendar_today,
                  title: 'Days',
                  value: '${_currentItinerary.totalDays}',
                  color: const Color(0xFFEF4444),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard({
    required IconData icon,
    required String title,
    required String value,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Icon(
            icon,
            size: 24,
            color: color,
          ),
          const SizedBox(height: 8),
          Text(
            value,
            style: GoogleFonts.instrumentSans(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: const Color(0xFF2D3748),
            ),
          ),
          Text(
            title,
            style: GoogleFonts.instrumentSans(
              fontSize: 12,
              color: const Color(0xFF718096),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCircularBottomNavigation() {
    return AnimatedBuilder(
      animation: _navAnimationController,
      builder: (context, child) {
        return Transform.translate(
          offset: Offset(
              0, (1 - _navAnimationController.value) * 10), // Reduced from 20
          child: AnimatedContainer(
            duration: const Duration(milliseconds: 200), // Reduced from 300ms
            curve: Curves.easeInOut,
            margin: const EdgeInsets.all(16),
            padding: const EdgeInsets.symmetric(vertical: 12),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(30),
              boxShadow: [
                // Simplified single shadow for better performance
                BoxShadow(
                  color: Colors.black.withOpacity(0.08), // Reduced opacity
                  blurRadius: 12, // Reduced from 20
                  offset: const Offset(0, 3), // Reduced from 4
                ),
              ],
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                _buildNavItem(
                  icon: Icons.map,
                  label: 'Itinerary',
                  index: 0,
                ),
                _buildNavItem(
                  icon: Icons.chat_bubble,
                  label: 'Comments',
                  index: 1,
                ),
                _buildNavItem(
                  icon: Icons.share,
                  label: 'Share',
                  index: 2,
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildNavItem({
    required IconData icon,
    required String label,
    required int index,
  }) {
    final isSelected = _currentTabIndex == index;

    return GestureDetector(
      onTap: () {
        _onNavItemTapped(index);
      },
      child: AnimatedBuilder(
        animation: _navItemControllers[index],
        builder: (context, child) {
          return Transform.scale(
            scale: _navItemScaleAnimations[index].value,
            child: FadeTransition(
              opacity: _navItemFadeAnimations[index],
              child: AnimatedContainer(
                duration:
                    const Duration(milliseconds: 150), // Reduced from 300ms
                curve: Curves.easeInOut,
                padding:
                    const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
                decoration: BoxDecoration(
                  color:
                      isSelected ? const Color(0xFF0D76FF) : Colors.transparent,
                  borderRadius: BorderRadius.circular(20),
                  border: isSelected
                      ? Border.all(
                          color: const Color(0xFF0D76FF).withOpacity(0.2),
                          width: 1,
                        )
                      : null,
                  boxShadow: isSelected
                      ? [
                          // Simplified single shadow for better performance
                          BoxShadow(
                            color: const Color(0xFF0D76FF)
                                .withOpacity(0.25), // Reduced opacity
                            blurRadius: 6, // Reduced from 8
                            offset: const Offset(0, 2),
                          ),
                        ]
                      : null,
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    AnimatedContainer(
                      duration: const Duration(
                          milliseconds: 150), // Reduced from 300ms
                      curve: Curves.easeInOut,
                      child: Icon(
                        icon,
                        size: 20,
                        color:
                            isSelected ? Colors.white : const Color(0xFF718096),
                      ),
                    ),
                    AnimatedSize(
                      duration: const Duration(
                          milliseconds: 150), // Reduced from 300ms
                      curve: Curves.easeInOut,
                      child: isSelected
                          ? Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                const SizedBox(width: 8),
                                FadeTransition(
                                  opacity: _navItemFadeAnimations[index],
                                  child: SlideTransition(
                                    position: Tween<Offset>(
                                      begin: const Offset(-0.5, 0),
                                      end: Offset.zero,
                                    ).animate(CurvedAnimation(
                                      parent: _navItemControllers[index],
                                      curve: Curves.easeInOut,
                                    )),
                                    child: Text(
                                      label,
                                      style: GoogleFonts.instrumentSans(
                                        fontSize: 14,
                                        fontWeight: FontWeight.w600,
                                        color: Colors.white,
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            )
                          : const SizedBox.shrink(),
                    ),
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  void _onNavItemTapped(int index) {
    if (_currentTabIndex != index) {
      // Add haptic feedback for better user experience
      HapticFeedback.lightImpact();

      // Simplified animation - just forward the tapped item
      _navItemControllers[index].forward();

      // Animate out the current tab
      _navItemControllers[_currentTabIndex].reverse();

      // Update the current tab index
      setState(() {
        _currentTabIndex = index;
      });

      // Simplified PageView transition
      _pageController.animateToPage(
        index,
        duration: const Duration(milliseconds: 200), // Reduced from 300ms
        curve: Curves.easeInOut,
      );

      // Delay the new tab animation slightly for better coordination
      Future.delayed(const Duration(milliseconds: 100), () {
        if (mounted) {
          _navItemControllers[index].forward();
        }
      });
    } else {
      // Add a subtle pulse animation when tapping the already active tab
      HapticFeedback.selectionClick();
      _navItemControllers[index].forward().then((_) {
        _navItemControllers[index].reverse().then((_) {
          _navItemControllers[index].forward();
        });
      });
    }
  }

  Future<void> _addComment() async {
    final content = _commentController.text.trim();
    if (content.isEmpty) return;

    _commentController.clear();

    try {
      final newComment = await CollaborativeItineraryService.addComment(
        itineraryId: _currentItinerary.id,
        content: content,
      );

      if (newComment != null) {
        // Immediately refresh comments as fallback
        await _loadComments();

        // Show success feedback
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'Comment added successfully',
                style: GoogleFonts.instrumentSans(color: Colors.white),
              ),
              backgroundColor: const Color(0xFF10B981),
              behavior: SnackBarBehavior.floating,
              duration: const Duration(seconds: 2),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(10),
              ),
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Failed to add comment: ${e.toString().replaceAll('Exception: ', '')}',
              style: GoogleFonts.instrumentSans(color: Colors.white),
            ),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
          ),
        );
      }
    }
  }

  void _showDeleteCommentDialog(ItineraryComment comment) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          title: Text(
            'Delete Comment',
            style: GoogleFonts.instrumentSans(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: const Color(0xFF2D3748),
            ),
          ),
          content: Text(
            'Are you sure you want to delete this comment? This action cannot be undone.',
            style: GoogleFonts.instrumentSans(
              fontSize: 14,
              color: const Color(0xFF718096),
              height: 1.4,
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(
                'Cancel',
                style: GoogleFonts.instrumentSans(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: const Color(0xFF718096),
                ),
              ),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                _deleteComment(comment);
              },
              child: Text(
                'Delete',
                style: GoogleFonts.instrumentSans(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: Colors.red,
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  Future<void> _deleteComment(ItineraryComment comment) async {
    try {
      final success =
          await CollaborativeItineraryService.deleteComment(comment.id);

      if (success && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Comment deleted successfully',
              style: GoogleFonts.instrumentSans(color: Colors.white),
            ),
            backgroundColor: const Color(0xFF10B981),
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Failed to delete comment: ${e.toString().replaceAll('Exception: ', '')}',
              style: GoogleFonts.instrumentSans(color: Colors.white),
            ),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
          ),
        );
      }
    }
  }

  void _handleItineraryDeleted() {
    if (!mounted) return;

    // Show notification that itinerary was deleted
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        title: Row(
          children: [
            const Icon(
              Icons.info_outline,
              color: Color(0xFF0D76FF),
              size: 24,
            ),
            const SizedBox(width: 12),
            Text(
              'Itinerary Deleted',
              style: GoogleFonts.instrumentSans(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: const Color(0xFF2D3748),
              ),
            ),
          ],
        ),
        content: Text(
          'This collaborative itinerary has been deleted by the owner. You will be redirected to the homepage.',
          style: GoogleFonts.instrumentSans(
            fontSize: 14,
            color: const Color(0xFF718096),
            height: 1.4,
          ),
        ),
        actions: [
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              // Unsubscribe from real-time updates to prevent conflicts
              _itinerarySubscription?.cancel();
              _commentsSubscription?.cancel();
              CollaborativeItineraryService.unsubscribeAll();
              // Navigate with slide-in animation
              _navigateToHomepage();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF0D76FF),
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              elevation: 0,
            ),
            child: Text(
              'Go to Homepage',
              style: GoogleFonts.instrumentSans(
                fontSize: 14,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDeleteSection() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.red.withOpacity(0.05),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Colors.red.withOpacity(0.2),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(
                Icons.warning_amber_rounded,
                color: Colors.red,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                'Danger Zone',
                style: GoogleFonts.instrumentSans(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.red.shade700,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            'Delete this collaborative itinerary permanently. This action cannot be undone and will remove the itinerary for all participants.',
            style: GoogleFonts.instrumentSans(
              fontSize: 14,
              color: Colors.red.shade600,
              height: 1.4,
            ),
          ),
          const SizedBox(height: 16),
          SizedBox(
            width: double.infinity,
            child: ElevatedButton.icon(
              onPressed: _isDeleting ? null : _showDeleteConfirmation,
              icon: _isDeleting
                  ? const SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    )
                  : const Icon(Icons.delete_forever, size: 18),
              label: Text(
                _isDeleting ? 'Deleting...' : 'Delete Collaboration',
                style: GoogleFonts.instrumentSans(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                ),
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                elevation: 0,
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Owner validation and delete functionality
  bool get _isCurrentUserOwner {
    final currentUser = AuthService.currentUser;
    return currentUser != null && currentUser.id == _currentItinerary.ownerId;
  }

  void _showDeleteConfirmation() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        title: Row(
          children: [
            Icon(
              Icons.warning_amber_rounded,
              color: Colors.red,
              size: 24,
            ),
            const SizedBox(width: 12),
            Text(
              'Delete Collaboration',
              style: GoogleFonts.instrumentSans(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: const Color(0xFF2D3748),
              ),
            ),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Are you sure you want to delete this collaborative itinerary?',
              style: GoogleFonts.instrumentSans(
                fontSize: 14,
                color: const Color(0xFF2D3748),
                height: 1.4,
              ),
            ),
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.red.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: Colors.red.withOpacity(0.3),
                  width: 1,
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'This action will:',
                    style: GoogleFonts.instrumentSans(
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                      color: Colors.red.shade700,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    '• Remove the itinerary for all participants\n• Delete all comments and collaboration data\n• Cannot be undone',
                    style: GoogleFonts.instrumentSans(
                      fontSize: 12,
                      color: Colors.red.shade600,
                      height: 1.3,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(
              'Cancel',
              style: GoogleFonts.instrumentSans(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: const Color(0xFF718096),
              ),
            ),
          ),
          ElevatedButton(
            onPressed: _isDeleting ? null : _deleteItinerary,
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              elevation: 0,
            ),
            child: _isDeleting
                ? SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                : Text(
                    'Delete',
                    style: GoogleFonts.instrumentSans(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
          ),
        ],
      ),
    );
  }

  Future<void> _deleteItinerary() async {
    setState(() {
      _isDeleting = true;
    });

    try {
      // Close the confirmation dialog first
      Navigator.of(context).pop();

      // Delete the collaborative itinerary
      await _deleteCollaborativeItinerary();

      // Show success message and navigate immediately
      if (mounted) {
        // Unsubscribe from real-time updates to prevent conflicts
        _itinerarySubscription?.cancel();
        _commentsSubscription?.cancel();
        CollaborativeItineraryService.unsubscribeAll();

        // Show success message
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Collaborative itinerary deleted successfully',
              style: GoogleFonts.instrumentSans(color: Colors.white),
            ),
            backgroundColor: const Color(0xFF10B981),
            behavior: SnackBarBehavior.floating,
            duration: const Duration(seconds: 3),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
          ),
        );

        // Navigate back to homepage with slide-in Figma-style animation
        _navigateToHomepage();
      }
    } catch (e) {
      setState(() {
        _isDeleting = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Failed to delete itinerary: ${e.toString().replaceAll('Exception: ', '')}',
              style: GoogleFonts.instrumentSans(color: Colors.white),
            ),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
            duration: const Duration(seconds: 4),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
          ),
        );
      }
    }
  }

  Future<void> _deleteCollaborativeItinerary() async {
    try {
      // Use the service method which includes ownership validation
      await CollaborativeItineraryService.deleteItinerary(_currentItinerary.id);
    } catch (e) {
      throw Exception('Failed to delete collaborative itinerary: $e');
    }
  }

  /// Navigate to homepage with slide-in Figma-style animation
  void _navigateToHomepage() {
    Navigator.of(context).pushReplacement(
      PageRouteBuilder(
        pageBuilder: (context, animation, secondaryAnimation) {
          return const HomepageScreen();
        },
        transitionsBuilder: (context, animation, secondaryAnimation, child) {
          // Slide-in animation from right to left (Figma-style)
          const begin = Offset(1.0, 0.0);
          const end = Offset.zero;
          const curve = Curves.easeInOut;

          var tween = Tween(begin: begin, end: end).chain(
            CurveTween(curve: curve),
          );

          return SlideTransition(
            position: animation.drive(tween),
            child: child,
          );
        },
        transitionDuration: const Duration(milliseconds: 300),
        settings: const RouteSettings(name: '/homepage'),
      ),
    );
  }

  // Helper methods for timeline functionality
  DateTime _parseDisplayDate(String dateString) {
    try {
      // Try parsing as DD/MM/YYYY first
      if (RegExp(r'^\d{2}/\d{2}/\d{4}$').hasMatch(dateString)) {
        final parts = dateString.split('/');
        final day = int.parse(parts[0]);
        final month = int.parse(parts[1]);
        final year = int.parse(parts[2]);
        return DateTime(year, month, day);
      }

      // Fallback to standard DateTime.parse for other formats
      return DateTime.parse(dateString);
    } catch (e) {
      // If all else fails, return current date
      return DateTime.now();
    }
  }

  String _getMonthName(DateTime date) {
    const months = [
      'Jan',
      'Feb',
      'Mar',
      'Apr',
      'May',
      'Jun',
      'Jul',
      'Aug',
      'Sep',
      'Oct',
      'Nov',
      'Dec'
    ];
    return '${date.day} ${months[date.month - 1]}';
  }

  Widget _buildTimeAxis() {
    // Get activities for the selected day to calculate dynamic height
    final activitiesForDay = _getActivitiesForSelectedDay();

    // Calculate dynamic height based on activity widgets
    final dynamicHeight = _calculateDynamicTimelineHeight(activitiesForDay);

    // Generate dynamic time markers based on activities
    final timeMarkers = _generateDynamicTimeMarkers(activitiesForDay);

    return SizedBox(
      width: 60,
      height: dynamicHeight,
      child: Stack(
        children: timeMarkers.map((marker) {
          return Positioned(
            top: marker['position'],
            left: 0,
            right: 0,
            child: Container(
              alignment: Alignment.topCenter,
              child: Text(
                marker['displayTime'],
                style: GoogleFonts.instrumentSans(
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                  color: const Color(0xFF718096),
                ),
              ),
            ),
          );
        }).toList(),
      ),
    );
  }

  Widget _buildActivitiesColumn() {
    // Get activities for the selected day
    final activitiesForDay = _getActivitiesForSelectedDay();

    // Calculate dynamic height based on activity widgets
    final dynamicHeight = _calculateDynamicTimelineHeight(activitiesForDay);

    // Generate dynamic time markers
    final timeMarkers = _generateDynamicTimeMarkers(activitiesForDay);

    return SizedBox(
      height: dynamicHeight,
      child: Stack(
        children: [
          // Timeline line
          Positioned(
            left: 0,
            top: 0,
            bottom: 0,
            child: Container(
              width: 2,
              color: const Color(0xFFE2E8F0),
            ),
          ),
          // Dynamic time markers
          ...timeMarkers.map((marker) {
            return Positioned(
              left: 0,
              top: marker['position'],
              child: Container(
                width: 8,
                height: 2,
                color: const Color(0xFFE2E8F0),
              ),
            );
          }),
          // Dynamic time slot drop targets
          ...timeMarkers.map((marker) {
            final time = marker['time'] as TimeOfDay;
            return Positioned(
              left: 16,
              top: marker['position'],
              right: 0,
              height: 60,
              child: _buildTimeSlotDropTargetForTime(time),
            );
          }),
          // Activities positioned dynamically
          ..._buildDynamicActivityWidgets(activitiesForDay),
        ],
      ),
    );
  }

  List<Map<String, String>> _getActivitiesForSelectedDay() {
    final selectedDay = _selectedDayIndex + 1;
    final activities = <Map<String, String>>[];

    // Check if we have day-specific activities
    if (_currentItinerary.daySpecificActivities != null &&
        _currentItinerary.daySpecificActivities!.containsKey(selectedDay)) {
      final dayActivities =
          _currentItinerary.daySpecificActivities![selectedDay]!;

      for (final destination in dayActivities.keys) {
        final destinationActivities = dayActivities[destination] ?? [];

        for (final activity in destinationActivities) {
          // Get activity times if available
          final activityTimes =
              _currentItinerary.activityTimes?[destination]?[activity];

          activities.add({
            'destination': destination,
            'name': activity,
            'startTime': activityTimes?['startTime'] ?? '09:00',
            'endTime': activityTimes?['endTime'] ?? '10:00',
            'day': selectedDay.toString(),
          });
        }
      }
    }

    return activities;
  }

  double _calculateDynamicTimelineHeight(List<Map<String, String>> activities) {
    // Generate time markers to get the total count
    final timeMarkers = _generateDynamicTimeMarkers(activities);

    // Calculate height based on number of time markers with consistent 60px spacing
    // Add extra space at the end for better scrolling experience
    return (timeMarkers.length * 60.0) + 120.0; // 120px extra padding at bottom
  }

  List<Map<String, dynamic>> _generateDynamicTimeMarkers(
      List<Map<String, String>> activities) {
    List<TimeOfDay> allTimes = [];

    // Always show complete 24-hour timeline (12:00 AM to 11:00 PM)
    for (int hour = 0; hour < 24; hour++) {
      allTimes.add(TimeOfDay(hour: hour, minute: 0));
    }

    // Add sub-hour markers for activities with specific minute times
    for (final activity in activities) {
      final startTime = _parseTimeString(activity['startTime'] ?? '09:00');
      final endTime = _parseTimeString(activity['endTime'] ?? '10:00');

      // Add start time if it has minutes
      if (startTime.minute != 0) {
        allTimes.add(startTime);
      }

      // Add end time if it has minutes
      if (endTime.minute != 0) {
        allTimes.add(endTime);
      }
    }

    // Remove duplicates and sort
    final uniqueTimes = allTimes.toSet().toList()
      ..sort((a, b) {
        final aMinutes = a.hour * 60 + a.minute;
        final bMinutes = b.hour * 60 + b.minute;
        return aMinutes.compareTo(bMinutes);
      });

    // Generate markers with consistent 60-pixel spacing
    return uniqueTimes.asMap().entries.map((entry) {
      final index = entry.key;
      final time = entry.value;
      final position = index * 60.0; // Consistent 60-pixel spacing
      final displayTime = _formatTimeForAxis(time);

      return {
        'time': time,
        'position': position,
        'displayTime': displayTime,
      };
    }).toList();
  }

  TimeOfDay _parseTimeString(String timeString) {
    try {
      final parts = timeString.split(':');
      final hour = int.parse(parts[0]);
      final minute = parts.length > 1 ? int.parse(parts[1]) : 0;
      return TimeOfDay(hour: hour, minute: minute);
    } catch (e) {
      return const TimeOfDay(hour: 9, minute: 0); // Default to 9:00 AM
    }
  }

  String _formatTimeForAxis(TimeOfDay time) {
    if (time.hour == 0) return '12:00 AM';
    if (time.hour < 12) {
      return time.minute == 0
          ? '${time.hour}:00 AM'
          : '${time.hour}:${time.minute.toString().padLeft(2, '0')} AM';
    }
    if (time.hour == 12) {
      return time.minute == 0
          ? '12:00 PM'
          : '12:${time.minute.toString().padLeft(2, '0')} PM';
    }
    return time.minute == 0
        ? '${time.hour - 12}:00 PM'
        : '${time.hour - 12}:${time.minute.toString().padLeft(2, '0')} PM';
  }

  List<Widget> _buildDynamicActivityWidgets(
      List<Map<String, String>> activities) {
    if (activities.isEmpty) return [];

    List<Widget> widgets = [];
    double currentPosition = 0;

    // Sort activities by start time
    final sortedActivities = List<Map<String, String>>.from(activities);
    sortedActivities.sort((a, b) {
      final timeA = _parseTimeString(a['startTime'] ?? '09:00');
      final timeB = _parseTimeString(b['startTime'] ?? '09:00');
      return (timeA.hour * 60 + timeA.minute)
          .compareTo(timeB.hour * 60 + timeB.minute);
    });

    for (final activity in sortedActivities) {
      final startTime = _parseTimeString(activity['startTime'] ?? '09:00');
      final endTime = _parseTimeString(activity['endTime'] ?? '10:00');

      // Calculate actual duration in minutes
      final actualDurationMinutes =
          _calculateDurationInMinutes(startTime, endTime);

      // Minimum display height equivalent to 1h 45m (105 pixels)
      const minDisplayHeight = 105.0;

      // Use the larger of actual duration or minimum display height
      final displayHeight = actualDurationMinutes > minDisplayHeight
          ? actualDurationMinutes.toDouble()
          : minDisplayHeight;

      // Calculate position based on actual time using the original time position calculation
      final timePosition = _calculateTimePosition(startTime);

      // Determine final position (avoid overlaps)
      final finalPosition =
          timePosition < currentPosition ? currentPosition : timePosition;

      widgets.add(
        Positioned(
          left: 16,
          top: finalPosition,
          right: 0,
          child: Container(
            height: displayHeight,
            child: _buildActivityCard(activity),
          ),
        ),
      );

      currentPosition = finalPosition + displayHeight + 8; // Add 8px spacing
    }

    return widgets;
  }

  int _calculateDurationInMinutes(TimeOfDay startTime, TimeOfDay endTime) {
    final startMinutes = startTime.hour * 60 + startTime.minute;
    final endMinutes = endTime.hour * 60 + endTime.minute;
    return (endMinutes - startMinutes)
        .clamp(15, 480); // Min 15 minutes, max 8 hours
  }

  double _calculateTimePosition(TimeOfDay time) {
    final timeInMinutes = time.hour * 60 + time.minute;
    // Each hour is 60 pixels, so calculate position based on minutes
    return (timeInMinutes / 60.0) * 60.0;
  }

  Widget _buildActivityCard(Map<String, String> activity) {
    final destination = activity['destination'] ?? '';
    final activityName = activity['name'] ?? '';
    final startTime = activity['startTime'] ?? '09:00';
    final endTime = activity['endTime'] ?? '10:00';
    final day = activity['day'] ?? '1';

    return Draggable<Map<String, String>>(
      data: activity,
      onDragStarted: () {
        setState(() {
          _isDragging = true;
          _draggedActivityKey = '$destination-$activityName-$day';
        });
        HapticFeedback.lightImpact();
      },
      onDragEnd: (details) {
        setState(() {
          _isDragging = false;
          _draggedActivityKey = null;
        });
      },
      feedback: Material(
        elevation: 8,
        borderRadius: BorderRadius.circular(12),
        child: Container(
          width: MediaQuery.of(context).size.width - 64,
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.2),
                blurRadius: 20,
                offset: const Offset(0, 8),
              ),
            ],
          ),
          child: _buildActivityContent(
              activityName, destination, startTime, endTime,
              isDragging: true),
        ),
      ),
      childWhenDragging: Opacity(
        opacity: 0.3,
        child: Container(
          margin: const EdgeInsets.only(bottom: 8),
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.grey.withOpacity(0.2),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: const Color(0xFFE2E8F0),
              width: 1,
            ),
          ),
          child: _buildActivityContent(
              activityName, destination, startTime, endTime),
        ),
      ),
      child: DragTarget<Map<String, String>>(
        onAcceptWithDetails: (details) {
          _onActivityDroppedOnActivity(details.data, activity);
        },
        builder: (context, candidateData, rejectedData) {
          final isHovering = candidateData.isNotEmpty;
          return Container(
            margin: const EdgeInsets.only(bottom: 8),
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: isHovering
                    ? const Color(0xFF0D76FF)
                    : const Color(0xFFE2E8F0),
                width: isHovering ? 2 : 1,
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.05),
                  blurRadius: 10,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: _buildActivityContent(
                activityName, destination, startTime, endTime),
          );
        },
      ),
    );
  }

  Widget _buildActivityContent(
      String activityName, String destination, String startTime, String endTime,
      {bool isDragging = false}) {
    // Calculate duration in minutes
    final start = _parseTimeString(startTime);
    final end = _parseTimeString(endTime);
    final durationMinutes = _calculateDurationInMinutes(start, end);

    // Get image for this activity
    final imagePath =
        _currentItinerary.activityImages?[destination]?[activityName];
    final hasImage = imagePath != null && imagePath.isNotEmpty;

    // Determine layout based on duration (3 hours = 180 minutes) - match regular itinerary
    final useColumnLayout = durationMinutes >= 180;

    if (useColumnLayout && hasImage) {
      // Column layout for activities ≥ 3 hours with image - match regular itinerary layout
      return LayoutBuilder(
        builder: (context, constraints) {
          // Calculate dynamic image height based on available space
          final availableHeight = constraints.maxHeight;
          final textContentHeight = _calculateTextContentHeight();
          final padding = 16.0; // 8px spacing + 8px margins

          // Image takes up remaining space, with minimum of 60px and maximum of 200px
          // Reduce by 10 pixels for better spacing
          final imageHeight =
              (availableHeight - textContentHeight - padding - 10.0)
                  .clamp(60.0, 200.0);

          return SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                // Large dynamic-height image at the top
                Container(
                  width: double.infinity,
                  height: imageHeight,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: const Color(0xFF0D76FF).withOpacity(0.2),
                    ),
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(7),
                    child: _buildNetworkImage(
                      imageUrl: imagePath,
                      width: double.infinity,
                      height: imageHeight,
                    ),
                  ),
                ),
                const SizedBox(height: 8),
                // Activity details below image
                _buildActivityTextContent(
                    activityName, destination, startTime, endTime, isDragging),
              ],
            ),
          );
        },
      );
    } else if (!useColumnLayout && hasImage) {
      // Row layout for activities < 3 hours with image
      return Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Activity details on the left
          Expanded(
            flex: 2,
            child: _buildActivityTextContent(
                activityName, destination, startTime, endTime, isDragging),
          ),
          const SizedBox(width: 8),
          // Small thumbnail image on the right
          Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: const Color(0xFF0D76FF).withOpacity(0.2),
              ),
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(7),
              child: _buildNetworkImage(
                imageUrl: imagePath,
                width: 60,
                height: 60,
              ),
            ),
          ),
        ],
      );
    } else {
      // No image - use standard layout
      return _buildActivityTextContent(
          activityName, destination, startTime, endTime, isDragging);
    }
  }

  Widget _buildActivityTextContent(String activityName, String destination,
      String startTime, String endTime, bool isDragging) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          activityName,
          style: GoogleFonts.instrumentSans(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: const Color(0xFF2D3748),
          ),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
        const SizedBox(height: 4),
        Row(
          children: [
            const Icon(
              Icons.location_on,
              size: 12,
              color: Color(0xFF718096),
            ),
            const SizedBox(width: 4),
            Expanded(
              child: Text(
                destination,
                style: GoogleFonts.instrumentSans(
                  fontSize: 12,
                  color: const Color(0xFF718096),
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
        const SizedBox(height: 4),
        Row(
          children: [
            const Icon(
              Icons.access_time,
              size: 12,
              color: Color(0xFF0D76FF),
            ),
            const SizedBox(width: 4),
            GestureDetector(
              onTap: () {
                print(
                    'DEBUG: Time text tapped - destination: $destination, activity: $activityName');
                print('DEBUG: isDragging: $isDragging');
                print('DEBUG: _canEditItinerary(): ${_canEditItinerary()}');

                if (_canEditItinerary() && !isDragging) {
                  print('DEBUG: Showing duration edit dialog');
                  _showDurationEditDialog(
                      destination, activityName, startTime, endTime);
                } else {
                  print(
                      'DEBUG: Cannot edit - canEdit: ${_canEditItinerary()}, isDragging: $isDragging');
                }
              },
              child: Text(
                '$startTime - $endTime',
                style: GoogleFonts.instrumentSans(
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                  color: const Color(0xFF0D76FF),
                ),
              ),
            ),
            if (!isDragging) ...[
              const Spacer(),
              // Show image indicator if activity has an image
              if (_currentItinerary.activityImages?[destination]
                      ?[activityName] !=
                  null) ...[
                Container(
                  padding: const EdgeInsets.all(2),
                  decoration: BoxDecoration(
                    color: const Color(0xFF0D76FF).withOpacity(0.1),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: const Icon(
                    Icons.image,
                    size: 12,
                    color: Color(0xFF0D76FF),
                  ),
                ),
                const SizedBox(width: 4),
              ],
              // More icon button for additional notes
              GestureDetector(
                onTap: () => _showActivityNotes(destination, activityName),
                child: Container(
                  padding: const EdgeInsets.all(2),
                  decoration: BoxDecoration(
                    color: const Color(0xFF0D76FF).withOpacity(0.1),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: const Icon(
                    Icons.more_horiz,
                    size: 12,
                    color: Color(0xFF0D76FF),
                  ),
                ),
              ),
              const SizedBox(width: 4),
              const Icon(
                Icons.drag_indicator,
                size: 16,
                color: Color(0xFF718096),
              ),
            ],
          ],
        ),
      ],
    );
  }

  double _calculateTextContentHeight() {
    // Estimate text content height for dynamic image sizing
    // Title: 14px font + 4px spacing = 18px
    // Location row: 12px font + 4px spacing = 16px
    // Time row: 12px font = 12px
    // Total: ~46px + some padding
    return 50.0;
  }

  Widget _buildNetworkImage({
    required String imageUrl,
    required double width,
    required double height,
  }) {
    return Image.network(
      imageUrl,
      width: width,
      height: height,
      fit: BoxFit.cover,
      loadingBuilder: (context, child, loadingProgress) {
        if (loadingProgress == null) return child;
        return Container(
          width: width,
          height: height,
          color: const Color(0xFFF7F9FC),
          child: Center(
            child: SizedBox(
              width: 20,
              height: 20,
              child: CircularProgressIndicator(
                value: loadingProgress.expectedTotalBytes != null
                    ? loadingProgress.cumulativeBytesLoaded /
                        loadingProgress.expectedTotalBytes!
                    : null,
                color: const Color(0xFF0D76FF),
                strokeWidth: 2,
              ),
            ),
          ),
        );
      },
      errorBuilder: (context, error, stackTrace) {
        return Container(
          width: width,
          height: height,
          color: const Color(0xFFF7F9FC),
          child: Icon(
            Icons.broken_image,
            size: width > 100 ? 24 : 16,
            color: const Color(0xFF718096),
          ),
        );
      },
    );
  }

  bool _canEditItinerary() {
    final currentUser = AuthService.currentUser;
    print('DEBUG: _canEditItinerary - currentUser: ${currentUser?.id}');
    print('DEBUG: _canEditItinerary - ownerId: ${_currentItinerary.ownerId}');
    print(
        'DEBUG: _canEditItinerary - participants count: ${_participants.length}');

    if (currentUser == null) {
      print('DEBUG: _canEditItinerary - No current user');
      return false;
    }

    // First check if user is the owner (this should work even if participants query failed)
    final isOwner = currentUser.id == _currentItinerary.ownerId;
    print('DEBUG: _canEditItinerary - isOwner: $isOwner');

    // If user is owner, they can always edit
    if (isOwner) {
      print('DEBUG: _canEditItinerary - User is owner, allowing edit');
      return true;
    }

    // Check participants list for editor role (only if participants loaded successfully)
    final participantWithEditRole = _participants.any((p) =>
        p.userId == currentUser.id &&
        (p.role == 'owner' || p.role == 'editor'));
    print(
        'DEBUG: _canEditItinerary - participantWithEditRole: $participantWithEditRole');

    final canEdit = participantWithEditRole;
    print('DEBUG: _canEditItinerary - final result: $canEdit');

    return canEdit;
  }

  void _showImageUploadDialog(String destination, String activityName) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.6,
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              children: [
                Container(
                  width: 40,
                  height: 4,
                  decoration: BoxDecoration(
                    color: const Color(0xFFE2E8F0),
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
                const SizedBox(height: 20),
                Text(
                  'Add Activity Image',
                  style: GoogleFonts.instrumentSans(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: const Color(0xFF2D3748),
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  '$activityName - $destination',
                  style: GoogleFonts.instrumentSans(
                    fontSize: 14,
                    color: const Color(0xFF718096),
                  ),
                ),
                const SizedBox(height: 20),
                Expanded(
                  child: ImageUploadWidget(
                    itineraryId: _currentItinerary.id,
                    destination: destination,
                    activityName: activityName,
                    currentImageUrl: _currentItinerary
                        .activityImages?[destination]?[activityName],
                    onImageUploaded: (imageUrl) async {
                      Navigator.pop(context);
                      await _updateActivityImage(
                          destination, activityName, imageUrl);
                    },
                    onImageRemoved: () async {
                      Navigator.pop(context);
                      await _removeActivityImage(destination, activityName);
                    },
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Future<void> _updateActivityImage(
      String destination, String activityName, String imageUrl) async {
    try {
      await CollaborativeItineraryService.updateActivityImage(
        itineraryId: _currentItinerary.id,
        destination: destination,
        activityName: activityName,
        imageUrl: imageUrl,
      );

      // Force UI update by updating local state immediately
      if (mounted) {
        setState(() {
          // Update local activity images to reflect the change immediately
          if (_currentItinerary.activityImages == null) {
            _currentItinerary = _currentItinerary.copyWith(
              activityImages: {
                destination: {activityName: imageUrl}
              },
            );
          } else {
            final updatedImages = Map<String, Map<String, String>>.from(
                _currentItinerary.activityImages!);
            if (!updatedImages.containsKey(destination)) {
              updatedImages[destination] = {};
            }
            updatedImages[destination]![activityName] = imageUrl;

            _currentItinerary = _currentItinerary.copyWith(
              activityImages: updatedImages,
            );
          }
        });

        // Show success message
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Activity image updated successfully',
              style: GoogleFonts.instrumentSans(color: Colors.white),
            ),
            backgroundColor: const Color(0xFF10B981),
            behavior: SnackBarBehavior.floating,
            duration: const Duration(seconds: 2),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Failed to update image: ${e.toString().replaceAll('Exception: ', '')}',
              style: GoogleFonts.instrumentSans(color: Colors.white),
            ),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
            duration: const Duration(seconds: 3),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
          ),
        );
      }
    }
  }

  Future<void> _removeActivityImage(
      String destination, String activityName) async {
    try {
      // Get current image URL for deletion from storage
      final currentImageUrl =
          _currentItinerary.activityImages?[destination]?[activityName];

      // Remove from database
      await CollaborativeItineraryService.removeActivityImage(
        itineraryId: _currentItinerary.id,
        destination: destination,
        activityName: activityName,
      );

      // Delete from storage if it's a Supabase URL
      if (currentImageUrl != null &&
          ImageUploadService.isSupabaseStorageUrl(currentImageUrl)) {
        await ImageUploadService.deleteActivityImage(currentImageUrl);
      }

      // Force UI update by updating local state immediately
      if (mounted) {
        setState(() {
          if (_currentItinerary.activityImages != null) {
            final updatedImages = Map<String, Map<String, String>>.from(
                _currentItinerary.activityImages!);
            if (updatedImages.containsKey(destination)) {
              updatedImages[destination]?.remove(activityName);

              // Remove destination if no activities have images
              if (updatedImages[destination]?.isEmpty == true) {
                updatedImages.remove(destination);
              }
            }

            _currentItinerary = _currentItinerary.copyWith(
              activityImages: updatedImages.isEmpty ? null : updatedImages,
            );
          }
        });

        // Show success message
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Activity image removed successfully',
              style: GoogleFonts.instrumentSans(color: Colors.white),
            ),
            backgroundColor: const Color(0xFF10B981),
            behavior: SnackBarBehavior.floating,
            duration: const Duration(seconds: 2),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Failed to remove image: ${e.toString().replaceAll('Exception: ', '')}',
              style: GoogleFonts.instrumentSans(color: Colors.white),
            ),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
            duration: const Duration(seconds: 3),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
          ),
        );
      }
    }
  }

  Widget _buildTimeSlotDropTargetForTime(TimeOfDay time) {
    return DragTarget<Map<String, String>>(
      onAcceptWithDetails: (details) {
        _onActivityDroppedOnTime(details.data, time);
      },
      builder: (context, candidateData, rejectedData) {
        final isHovering = candidateData.isNotEmpty;
        return Container(
          decoration: isHovering
              ? BoxDecoration(
                  color: const Color(0xFF0D76FF).withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: const Color(0xFF0D76FF),
                    width: 2,
                  ),
                )
              : null,
          child: isHovering
              ? Center(
                  child: Text(
                    'Drop here for ${_formatTimeForAxis(time)}',
                    style: GoogleFonts.instrumentSans(
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                      color: const Color(0xFF0D76FF),
                    ),
                  ),
                )
              : null,
        );
      },
    );
  }

  void _onActivityDroppedOnTime(
      Map<String, String> draggedActivity, TimeOfDay time) {
    // Handle dropping activity on specific time (with minutes precision)
    HapticFeedback.mediumImpact();

    final destination = draggedActivity['destination'] ?? '';
    final activityName = draggedActivity['name'] ?? '';
    final currentDay = (_selectedDayIndex + 1).toString();

    // Get current duration to maintain it
    final currentStartTime =
        _parseTimeString(draggedActivity['startTime'] ?? '09:00');
    final currentEndTime =
        _parseTimeString(draggedActivity['endTime'] ?? '10:00');
    final durationMinutes =
        _calculateDurationInMinutes(currentStartTime, currentEndTime);

    // Set new start time based on dropped time
    final newStartTime = time;
    final newEndMinutes = (time.hour * 60 + time.minute) + durationMinutes;
    final newEndTime = TimeOfDay(
      hour: (newEndMinutes ~/ 60) % 24,
      minute: newEndMinutes % 60,
    );

    _updateActivityTime(
      destination,
      activityName,
      newStartTime,
      newEndTime,
      day: currentDay,
    );

    _saveActivityChanges();
  }

  // Drag and drop handling methods
  void _onActivityDroppedOnActivity(
      Map<String, String> draggedActivity, Map<String, String> targetActivity) {
    HapticFeedback.mediumImpact();

    final draggedDestination = draggedActivity['destination'] ?? '';
    final draggedName = draggedActivity['name'] ?? '';
    final targetDestination = targetActivity['destination'] ?? '';
    final targetName = targetActivity['name'] ?? '';

    // If same destination, swap times
    if (draggedDestination == targetDestination) {
      final draggedStartTime = draggedActivity['startTime'] ?? '09:00';
      final draggedEndTime = draggedActivity['endTime'] ?? '10:00';
      final targetStartTime = targetActivity['startTime'] ?? '09:00';
      final targetEndTime = targetActivity['endTime'] ?? '10:00';

      _updateActivityTime(
        draggedDestination,
        draggedName,
        _parseTimeString(targetStartTime),
        _parseTimeString(targetEndTime),
        day: targetActivity['day'],
      );

      _updateActivityTime(
        targetDestination,
        targetName,
        _parseTimeString(draggedStartTime),
        _parseTimeString(draggedEndTime),
        day: draggedActivity['day'],
      );

      _saveActivityChanges();
    }
  }

  Future<void> _updateActivityTime(String destination, String activity,
      TimeOfDay startTime, TimeOfDay endTime,
      {String? day}) async {
    try {
      final startTimeString =
          '${startTime.hour.toString().padLeft(2, '0')}:${startTime.minute.toString().padLeft(2, '0')}';
      final endTimeString =
          '${endTime.hour.toString().padLeft(2, '0')}:${endTime.minute.toString().padLeft(2, '0')}';

      // Use the collaborative service to update activity duration
      await CollaborativeItineraryService.updateActivityDuration(
        itineraryId: _currentItinerary.id,
        destination: destination,
        activityName: activity,
        startTime: startTimeString,
        endTime: endTimeString,
        day: day ?? (_selectedDayIndex + 1).toString(),
      );

      // Update local state immediately for responsive UI
      final updatedActivityTimes =
          Map<String, Map<String, Map<String, String>>>.from(
              _currentItinerary.activityTimes ?? {});

      if (!updatedActivityTimes.containsKey(destination)) {
        updatedActivityTimes[destination] = {};
      }

      updatedActivityTimes[destination]![activity] = {
        'startTime': startTimeString,
        'endTime': endTimeString,
        'day': day ?? (_selectedDayIndex + 1).toString(),
      };

      setState(() {
        _currentItinerary = _currentItinerary.copyWith(
          activityTimes: updatedActivityTimes,
        );
      });

      HapticFeedback.lightImpact();

      // Show success message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Activity duration updated successfully',
              style: GoogleFonts.instrumentSans(color: Colors.white),
            ),
            backgroundColor: const Color(0xFF10B981),
            behavior: SnackBarBehavior.floating,
            duration: const Duration(seconds: 2),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Failed to update activity duration: ${e.toString().replaceAll('Exception: ', '')}',
              style: GoogleFonts.instrumentSans(color: Colors.white),
            ),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
            duration: const Duration(seconds: 3),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
          ),
        );
      }
    }
  }

  Future<void> _saveActivityChanges() async {
    try {
      // Update the collaborative itinerary in Supabase directly
      final updateData = _currentItinerary.toJson();
      updateData['updated_at'] = DateTime.now().toIso8601String();

      await SupabaseConfig.client
          .from('collaborative_itineraries')
          .update(updateData)
          .eq('id', _currentItinerary.id);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Activity times updated successfully',
              style: GoogleFonts.instrumentSans(color: Colors.white),
            ),
            backgroundColor: const Color(0xFF10B981),
            behavior: SnackBarBehavior.floating,
            duration: const Duration(seconds: 2),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Failed to update activity times: ${e.toString().replaceAll('Exception: ', '')}',
              style: GoogleFonts.instrumentSans(color: Colors.white),
            ),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
          ),
        );
      }
    }
  }

  void _shareCode() {
    // TODO: Implement native sharing
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          'Share code: ${_currentItinerary.collaborationCode}',
          style: GoogleFonts.instrumentSans(color: Colors.white),
        ),
        backgroundColor: const Color(0xFF0D76FF),
      ),
    );
  }

  void _showDurationEditDialog(String destination, String activity,
      String currentStartTime, String currentEndTime) {
    print(
        'DEBUG: _showDurationEditDialog called for $activity at $destination');
    print('DEBUG: Start time: $currentStartTime, End time: $currentEndTime');

    final startTime = _parseTimeString(currentStartTime);
    final endTime = _parseTimeString(currentEndTime);
    int durationMinutes = _calculateDurationInMinutes(startTime, endTime);

    print(
        'DEBUG: Parsed times - Start: ${startTime.hour}:${startTime.minute}, End: ${endTime.hour}:${endTime.minute}');
    print('DEBUG: Duration: $durationMinutes minutes');
    print('DEBUG: About to show dialog...');

    // Show activity details in a SnackBar for now (dialog has layout issues)
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              activity,
              style: GoogleFonts.instrumentSans(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: Colors.white,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              'Scheduled: $currentStartTime - $currentEndTime',
              style: GoogleFonts.instrumentSans(
                fontSize: 14,
                color: Colors.white70,
              ),
            ),
            Text(
              'Duration: ${_formatDuration(durationMinutes)}',
              style: GoogleFonts.instrumentSans(
                fontSize: 14,
                color: Colors.white70,
              ),
            ),
          ],
        ),
        backgroundColor: const Color(0xFF0D76FF),
        duration: const Duration(seconds: 4),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        margin: const EdgeInsets.all(16),
      ),
    );
  }

  String _formatDuration(int minutes) {
    final hours = minutes ~/ 60;
    final remainingMinutes = minutes % 60;

    if (hours == 0) {
      return '${remainingMinutes}m';
    } else if (remainingMinutes == 0) {
      return '${hours}h';
    } else {
      return '${hours}h ${remainingMinutes}m';
    }
  }

  void _showActivityNotes(String destination, String activityName) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.6,
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              children: [
                Container(
                  width: 40,
                  height: 4,
                  decoration: BoxDecoration(
                    color: const Color(0xFFE2E8F0),
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
                const SizedBox(height: 20),
                Text(
                  'Activity Notes',
                  style: GoogleFonts.instrumentSans(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: const Color(0xFF2D3748),
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  '$activityName - $destination',
                  style: GoogleFonts.instrumentSans(
                    fontSize: 14,
                    color: const Color(0xFF718096),
                  ),
                ),
                const SizedBox(height: 20),
                Expanded(
                  child: Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: const Color(0xFFF7F9FC),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: const Color(0xFFE2E8F0),
                        width: 1,
                      ),
                    ),
                    child: Text(
                      'Additional notes and details for this activity can be added here in future updates.',
                      style: GoogleFonts.instrumentSans(
                        fontSize: 14,
                        color: const Color(0xFF718096),
                        fontStyle: FontStyle.italic,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
