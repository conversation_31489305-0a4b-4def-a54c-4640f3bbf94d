import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:image_picker/image_picker.dart';
import '../models/itinerary.dart';
import '../services/itinerary_service.dart';
import '../services/collaborative_itinerary_service.dart';
import '../services/auth_service.dart';
import 'edit_itinerary_screen.dart';

class ItineraryDetailScreen extends StatefulWidget {
  final Itinerary itinerary;

  const ItineraryDetailScreen({
    super.key,
    required this.itinerary,
  });

  @override
  State<ItineraryDetailScreen> createState() => _ItineraryDetailScreenState();
}

class _ItineraryDetailScreenState extends State<ItineraryDetailScreen>
    with TickerProviderStateMixin {
  late AnimationController _fadeController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  late Itinerary _currentItinerary;
  bool _isLoading = false;

  // Map to store activity times: destination -> activity -> {startTime, endTime, day}
  Map<String, Map<String, Map<String, String>>> _activityTimes = {};

  // Map to store activity images: destination -> activity -> imagePath
  Map<String, Map<String, String>> _activityImages = {};

  // Map to store activity notes: destination -> activity -> notes
  Map<String, Map<String, String>> _activityNotes = {};

  // Image picker instance
  final ImagePicker _imagePicker = ImagePicker();

  // Timeline-specific state
  List<DateTime> _tripDays = [];
  int _selectedDayIndex = 0;
  DateTime? _selectedDate;

  // Drag and drop state
  bool _isDragging = false;
  String? _draggedActivityKey;

  @override
  void initState() {
    super.initState();
    _currentItinerary = widget.itinerary;
    _initializeActivityTimes();
    _initializeActivityImages();
    _initializeTripDays();

    // Initialize animations
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeInOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(1.0, 0.0),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeInOut,
    ));

    // Start animation
    _fadeController.forward();
  }

  void _initializeActivityTimes() {
    // Load existing activity times from itinerary or initialize defaults
    if (_currentItinerary.activityTimes != null) {
      _activityTimes = Map<String, Map<String, Map<String, String>>>.from(
          _currentItinerary.activityTimes!);
      return;
    }

    // Initialize default times for activities (9 AM start, 1-hour intervals)
    if (_currentItinerary.usesDaySpecificActivities) {
      // Initialize times for day-specific activities
      for (int day = 1; day <= _currentItinerary.totalDays; day++) {
        int currentHour = 9;
        final activitiesForDay = _currentItinerary.getActivitiesForDay(day);

        for (final destination in activitiesForDay.keys) {
          if (!_activityTimes.containsKey(destination)) {
            _activityTimes[destination] = {};
          }

          final activities = activitiesForDay[destination] ?? [];
          for (final activity in activities) {
            final startTime = '${currentHour.toString().padLeft(2, '0')}:00';
            final endTime =
                '${(currentHour + 1).toString().padLeft(2, '0')}:00';

            _activityTimes[destination]![activity] = {
              'startTime': startTime,
              'endTime': endTime,
              'day': day.toString(),
            };

            currentHour++;
            if (currentHour > 22) currentHour = 9; // Reset to 9 AM if too late
          }
        }
      }
    } else {
      // Legacy initialization for backward compatibility
      int currentHour = 9;

      for (final destination in _currentItinerary.destinations) {
        final activities = _currentItinerary.dailyActivities[destination] ?? [];
        _activityTimes[destination] = {};

        for (final activity in activities) {
          final startTime = '${currentHour.toString().padLeft(2, '0')}:00';
          final endTime = '${(currentHour + 1).toString().padLeft(2, '0')}:00';

          _activityTimes[destination]![activity] = {
            'startTime': startTime,
            'endTime': endTime,
            'day': '1', // Default to day 1 for legacy activities
          };

          currentHour++;
          if (currentHour > 22) currentHour = 9; // Reset to 9 AM if too late
        }
      }
    }
  }

  void _initializeActivityImages() {
    // Load existing activity images from itinerary if available
    if (_currentItinerary.activityImages != null) {
      _activityImages = Map<String, Map<String, String>>.from(
          _currentItinerary.activityImages!);
    }
  }

  void _initializeTripDays() {
    try {
      // Parse DD/MM/YYYY format
      final startParts = _currentItinerary.startDate.split('/');
      final endParts = _currentItinerary.endDate.split('/');

      if (startParts.length == 3 && endParts.length == 3) {
        final startDate = DateTime(
          int.parse(startParts[2]), // year
          int.parse(startParts[1]), // month
          int.parse(startParts[0]), // day
        );
        final endDate = DateTime(
          int.parse(endParts[2]), // year
          int.parse(endParts[1]), // month
          int.parse(endParts[0]), // day
        );

        // Generate list of all days in the trip
        _tripDays = [];
        DateTime currentDay = startDate;
        while (currentDay.isBefore(endDate) ||
            currentDay.isAtSameMomentAs(endDate)) {
          _tripDays.add(currentDay);
          currentDay = currentDay.add(const Duration(days: 1));
        }

        // Set initial selected date to first day
        if (_tripDays.isNotEmpty) {
          _selectedDate = _tripDays[0];
          _selectedDayIndex = 0;
        }
      }
    } catch (e) {
      // If parsing fails, create a single day for today
      _tripDays = [DateTime.now()];
      _selectedDate = DateTime.now();
      _selectedDayIndex = 0;
    }
  }

  @override
  void dispose() {
    _fadeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xffFFFFFF),
      appBar: AppBar(
        backgroundColor: const Color(0xffFFFFFF),
        surfaceTintColor: const Color(0xffFFFFFF),
        shadowColor: Colors.transparent,
        elevation: 0,
        scrolledUnderElevation: 0,
        leading: IconButton(
          onPressed: () => Navigator.of(context).pop(),
          icon: const Icon(
            Icons.arrow_back_ios,
            color: Color(0xFF2D3748),
          ),
        ),
        title: Text(
          _currentItinerary.title,
          style: GoogleFonts.instrumentSans(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: const Color(0xFF2D3748),
          ),
        ),
        centerTitle: true,
        actions: [
          IconButton(
            onPressed: _showEditOptions,
            icon: const Icon(
              Icons.more_vert,
              color: Color(0xFF2D3748),
            ),
          ),
        ],
      ),
      body: FadeTransition(
        opacity: _fadeAnimation,
        child: SlideTransition(
          position: _slideAnimation,
          child: _isLoading
              ? const Center(
                  child: CircularProgressIndicator(
                    color: Color(0xFF0D76FF),
                  ),
                )
              : Column(
                  children: [
                    _buildDaySelector(),
                    Expanded(
                      child: _buildTimelineView(),
                    ),
                  ],
                ),
        ),
      ),
    );
  }

  Widget _buildDaySelector() {
    if (_tripDays.isEmpty) return const SizedBox.shrink();

    return Container(
      height: 140,
      margin: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text(
                'Select Day',
                style: GoogleFonts.instrumentSans(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: const Color(0xFF2D3748),
                ),
              ),
              const SizedBox(width: 8),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: const Color(0xFF0D76FF).withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  '${_tripDays.length} ${_tripDays.length == 1 ? 'day' : 'days'}',
                  style: GoogleFonts.instrumentSans(
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                    color: const Color(0xFF0D76FF),
                  ),
                ),
              ),
              const SizedBox(width: 8),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: const Color(0xFF10B981).withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  '${_getTotalActivitiesCount()} ${_getTotalActivitiesCount() == 1 ? 'Spot' : 'Spots'}',
                  style: GoogleFonts.instrumentSans(
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                    color: const Color(0xFF10B981),
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Expanded(
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: _tripDays.length,
              itemBuilder: (context, index) {
                final day = _tripDays[index];
                final isSelected = index == _selectedDayIndex;
                final isToday = _isToday(day);

                return GestureDetector(
                  onTap: () {
                    setState(() {
                      _selectedDayIndex = index;
                      _selectedDate = day;
                    });
                    HapticFeedback.lightImpact();
                  },
                  child: Container(
                    width: 90,
                    margin: const EdgeInsets.only(right: 12),
                    decoration: BoxDecoration(
                      color: isSelected
                          ? const Color(0xFF0D76FF)
                          : isToday
                              ? const Color(0xFF0D76FF).withOpacity(0.05)
                              : Colors.white,
                      borderRadius: BorderRadius.circular(16),
                      border: Border.all(
                        color: isSelected
                            ? const Color(0xFF0D76FF)
                            : isToday
                                ? const Color(0xFF0D76FF).withOpacity(0.3)
                                : const Color(0xFFE2E8F0),
                        width: isSelected || isToday ? 2 : 1,
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.05),
                          blurRadius: 8,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          'Day ${index + 1}',
                          style: GoogleFonts.instrumentSans(
                            fontSize: 11,
                            fontWeight: FontWeight.w600,
                            color: isSelected
                                ? Colors.white
                                : const Color(0xFF718096),
                          ),
                        ),
                        const SizedBox(height: 2),
                        Text(
                          _getDayName(day),
                          style: GoogleFonts.instrumentSans(
                            fontSize: 12,
                            fontWeight: FontWeight.w600,
                            color: isSelected
                                ? Colors.white
                                : isToday
                                    ? const Color(0xFF0D76FF)
                                    : const Color(0xFF718096),
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          '${day.day}',
                          style: GoogleFonts.instrumentSans(
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                            color: isSelected
                                ? Colors.white
                                : const Color(0xFF2D3748),
                          ),
                        ),
                        Text(
                          _getMonthName(day),
                          style: GoogleFonts.instrumentSans(
                            fontSize: 10,
                            fontWeight: FontWeight.w500,
                            color: isSelected
                                ? Colors.white
                                : const Color(0xFF718096),
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTimelineView() {
    return SingleChildScrollView(
      physics: const BouncingScrollPhysics(),
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 16),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildTimeAxis(),
            const SizedBox(width: 16),
            Expanded(
              child: _buildActivitiesColumn(),
            ),
          ],
        ),
      ),
    );
  }

  String _getDayName(DateTime date) {
    const days = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
    return days[date.weekday % 7];
  }

  String _getMonthName(DateTime date) {
    const months = [
      'Jan',
      'Feb',
      'Mar',
      'Apr',
      'May',
      'Jun',
      'Jul',
      'Aug',
      'Sep',
      'Oct',
      'Nov',
      'Dec'
    ];
    return months[date.month - 1];
  }

  bool _isToday(DateTime date) {
    final now = DateTime.now();
    return date.year == now.year &&
        date.month == now.month &&
        date.day == now.day;
  }

  Widget _buildTimeSlotDropTarget(int hour) {
    return DragTarget<Map<String, String>>(
      onAcceptWithDetails: (details) {
        _onActivityDroppedOnTimeSlot(details.data, hour);
      },
      builder: (context, candidateData, rejectedData) {
        final isHovering = candidateData.isNotEmpty;
        return Container(
          decoration: isHovering
              ? BoxDecoration(
                  color: const Color(0xFF0D76FF).withOpacity(0.1),
                  border: Border.all(
                    color: const Color(0xFF0D76FF),
                    width: 2,
                  ),
                  borderRadius: BorderRadius.circular(8),
                )
              : null,
          child: isHovering
              ? Center(
                  child: Text(
                    'Drop here for ${_formatHour(hour)}',
                    style: GoogleFonts.instrumentSans(
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                      color: const Color(0xFF0D76FF),
                    ),
                  ),
                )
              : null,
        );
      },
    );
  }

  Widget _buildTimeSlotDropTargetForTime(TimeOfDay time) {
    return DragTarget<Map<String, String>>(
      onAcceptWithDetails: (details) {
        _onActivityDroppedOnTime(details.data, time);
      },
      builder: (context, candidateData, rejectedData) {
        final isHovering = candidateData.isNotEmpty;
        return Container(
          decoration: isHovering
              ? BoxDecoration(
                  color: const Color(0xFF0D76FF).withOpacity(0.1),
                  border: Border.all(
                    color: const Color(0xFF0D76FF),
                    width: 2,
                  ),
                  borderRadius: BorderRadius.circular(8),
                )
              : null,
          child: isHovering
              ? Center(
                  child: Text(
                    'Drop here for ${_formatTimeForAxis(time)}',
                    style: GoogleFonts.instrumentSans(
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                      color: const Color(0xFF0D76FF),
                    ),
                  ),
                )
              : null,
        );
      },
    );
  }

  String _formatHour(int hour) {
    if (hour == 0) return '12:00 AM';
    if (hour < 12) return '${hour}:00 AM';
    if (hour == 12) return '12:00 PM';
    return '${hour - 12}:00 PM';
  }

  Widget _buildTimeAxis() {
    // Get activities for the selected day to calculate dynamic height
    final activitiesForDay = _getActivitiesForSelectedDay();

    // Calculate dynamic height based on activity widgets
    final dynamicHeight = _calculateDynamicTimelineHeight(activitiesForDay);

    // Generate dynamic time markers based on activities
    final timeMarkers = _generateDynamicTimeMarkers(activitiesForDay);

    return SizedBox(
      width: 60,
      height: dynamicHeight,
      child: Stack(
        children: timeMarkers.map((marker) {
          return Positioned(
            top: marker['position'],
            left: 0,
            right: 0,
            child: Container(
              alignment: Alignment.topCenter,
              child: Text(
                marker['displayTime'],
                style: GoogleFonts.instrumentSans(
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                  color: const Color(0xFF718096),
                ),
              ),
            ),
          );
        }).toList(),
      ),
    );
  }

  Widget _buildActivitiesColumn() {
    // Get activities for the selected day
    final activitiesForDay = _getActivitiesForSelectedDay();

    // Calculate dynamic height based on activity widgets
    final dynamicHeight = _calculateDynamicTimelineHeight(activitiesForDay);

    // Generate dynamic time markers
    final timeMarkers = _generateDynamicTimeMarkers(activitiesForDay);

    return SizedBox(
      height: dynamicHeight,
      child: Stack(
        children: [
          // Timeline line
          Positioned(
            left: 0,
            top: 0,
            bottom: 0,
            child: Container(
              width: 2,
              color: const Color(0xFFE2E8F0),
            ),
          ),
          // Dynamic time markers
          ...timeMarkers.map((marker) {
            return Positioned(
              left: 0,
              top: marker['position'],
              child: Container(
                width: 8,
                height: 2,
                color: const Color(0xFFE2E8F0),
              ),
            );
          }),
          // Dynamic time slot drop targets
          ...timeMarkers.map((marker) {
            final time = marker['time'] as TimeOfDay;
            return Positioned(
              left: 16,
              top: marker['position'],
              right: 0,
              height: 60,
              child: _buildTimeSlotDropTargetForTime(time),
            );
          }),
          // Activities positioned dynamically
          ..._buildDynamicActivityWidgets(activitiesForDay),
        ],
      ),
    );
  }

  List<Map<String, String>> _getActivitiesForSelectedDay() {
    if (_selectedDate == null) return [];

    List<Map<String, String>> dayActivities = [];

    // Check if this itinerary uses day-specific activities
    if (_currentItinerary.usesDaySpecificActivities) {
      // Get the day number (1-based) from the selected date
      final dayNumber = _selectedDayIndex + 1;
      final activitiesForDay = _currentItinerary.getActivitiesForDay(dayNumber);

      for (final destination in activitiesForDay.keys) {
        final activities = activitiesForDay[destination] ?? [];
        for (final activity in activities) {
          final activityTime = _activityTimes[destination]?[activity];
          dayActivities.add({
            'name': activity,
            'destination': destination,
            'startTime': activityTime?['startTime'] ?? '09:00',
            'endTime': activityTime?['endTime'] ?? '10:00',
            'day': dayNumber.toString(),
          });
        }
      }
    } else {
      // Fallback to legacy format - show all activities for backward compatibility
      for (final destination in _currentItinerary.destinations) {
        final activities = _currentItinerary.dailyActivities[destination] ?? [];
        for (final activity in activities) {
          final activityTime = _activityTimes[destination]?[activity];
          dayActivities.add({
            'name': activity,
            'destination': destination,
            'startTime': activityTime?['startTime'] ?? '09:00',
            'endTime': activityTime?['endTime'] ?? '10:00',
            'day': '1', // Default to day 1 for legacy itineraries
          });
        }
      }
    }

    // Sort activities by start time
    dayActivities.sort((a, b) {
      final timeA = _parseTimeString(a['startTime'] ?? '09:00');
      final timeB = _parseTimeString(b['startTime'] ?? '09:00');
      return (timeA.hour * 60 + timeA.minute)
          .compareTo(timeB.hour * 60 + timeB.minute);
    });

    return dayActivities;
  }

  TimeOfDay _parseTimeString(String timeString) {
    try {
      final parts = timeString.split(':');
      if (parts.length == 2) {
        final hour = int.parse(parts[0]);
        final minute = int.parse(parts[1]);
        return TimeOfDay(hour: hour, minute: minute);
      }
    } catch (e) {
      // If parsing fails, return default time
    }
    return const TimeOfDay(hour: 9, minute: 0);
  }

  double _calculateTimePosition(TimeOfDay time) {
    // Each hour takes 60 pixels
    final totalMinutes = time.hour * 60 + time.minute;
    return totalMinutes.toDouble();
  }

  List<Map<String, dynamic>> _generateDynamicTimeMarkers(
      List<Map<String, String>> activities) {
    List<TimeOfDay> allTimes = [];

    // Always show complete 24-hour timeline (12:00 AM to 11:00 PM)
    for (int hour = 0; hour < 24; hour++) {
      allTimes.add(TimeOfDay(hour: hour, minute: 0));
    }

    // Add sub-hour markers for activities with specific minute times
    for (final activity in activities) {
      final startTime = _parseTimeString(activity['startTime'] ?? '09:00');
      final endTime = _parseTimeString(activity['endTime'] ?? '10:00');

      // Add start time if it has minutes
      if (startTime.minute != 0) {
        allTimes.add(startTime);
      }

      // Add end time if it has minutes
      if (endTime.minute != 0) {
        allTimes.add(endTime);
      }
    }

    // Remove duplicates and sort
    final uniqueTimes = allTimes.toSet().toList()
      ..sort((a, b) {
        final aMinutes = a.hour * 60 + a.minute;
        final bMinutes = b.hour * 60 + b.minute;
        return aMinutes.compareTo(bMinutes);
      });

    // Generate markers with consistent 60-pixel spacing
    return uniqueTimes.asMap().entries.map((entry) {
      final index = entry.key;
      final time = entry.value;
      final position = index * 60.0; // Consistent 60-pixel spacing
      final displayTime = _formatTimeForAxis(time);

      return {
        'time': time,
        'position': position,
        'displayTime': displayTime,
      };
    }).toList();
  }

  String _formatTimeForAxis(TimeOfDay time) {
    if (time.minute == 0) {
      // Show full hour format
      if (time.hour == 0) return '12 AM';
      if (time.hour < 12) return '${time.hour} AM';
      if (time.hour == 12) return '12 PM';
      return '${time.hour - 12} PM';
    } else {
      // Show hour:minute format
      final hourStr = time.hour == 0
          ? '12'
          : time.hour <= 12
              ? '${time.hour}'
              : '${time.hour - 12}';
      final minuteStr = time.minute.toString().padLeft(2, '0');
      final period = time.hour < 12 ? 'AM' : 'PM';
      return '$hourStr:$minuteStr $period';
    }
  }

  double _calculateDuration(TimeOfDay startTime, TimeOfDay endTime) {
    final startMinutes = startTime.hour * 60 + startTime.minute;
    final endMinutes = endTime.hour * 60 + endTime.minute;
    final durationMinutes = endMinutes - startMinutes;

    // Minimum height of 40 pixels, maximum based on actual duration
    return (durationMinutes > 0 ? durationMinutes.toDouble() : 60.0)
        .clamp(40.0, double.infinity);
  }

  int _calculateDurationInMinutes(TimeOfDay startTime, TimeOfDay endTime) {
    final startMinutes = startTime.hour * 60 + startTime.minute;
    final endMinutes = endTime.hour * 60 + endTime.minute;
    final durationMinutes = endMinutes - startMinutes;

    // Handle overnight activities
    if (durationMinutes < 0) {
      return (24 * 60) + durationMinutes;
    }

    return durationMinutes > 0 ? durationMinutes : 60; // Default to 1 hour
  }

  String _formatDuration(int minutes) {
    if (minutes < 60) {
      return '${minutes}m';
    } else {
      final hours = minutes ~/ 60;
      final remainingMinutes = minutes % 60;
      if (remainingMinutes == 0) {
        return '${hours}h';
      } else {
        return '${hours}h ${remainingMinutes}m';
      }
    }
  }

  double _calculateTextContentHeight() {
    // Estimate the height needed for text content in activity widgets
    // This includes: title (1 line max), location row, time row, and spacing
    const titleHeight = 14.0; // 14px font size, 1 line only
    const locationRowHeight = 16.0; // 12px font + 4px icon spacing
    const timeRowHeight = 16.0; // 12px font + 4px icon spacing
    const verticalSpacing = 8.0; // 4px + 4px between elements

    return titleHeight + locationRowHeight + timeRowHeight + verticalSpacing;
  }

  int _getTotalActivitiesCount() {
    int totalCount = 0;

    // Check if this itinerary uses day-specific activities
    if (_currentItinerary.usesDaySpecificActivities) {
      // Count activities across all days
      for (int day = 1; day <= _currentItinerary.totalDays; day++) {
        final activitiesForDay = _currentItinerary.getActivitiesForDay(day);
        for (final destination in activitiesForDay.keys) {
          final activities = activitiesForDay[destination] ?? [];
          totalCount += activities.length;
        }
      }
    } else {
      // Legacy format - count all activities in dailyActivities
      for (final destination in _currentItinerary.destinations) {
        final activities = _currentItinerary.dailyActivities[destination] ?? [];
        totalCount += activities.length;
      }
    }

    return totalCount;
  }

  double _calculateDynamicTimelineHeight(List<Map<String, String>> activities) {
    // Generate time markers to get the total count
    final timeMarkers = _generateDynamicTimeMarkers(activities);

    // Calculate height based on number of time markers with consistent 60px spacing
    // Add extra space at the end for better scrolling experience
    return (timeMarkers.length * 60.0) + 120.0; // 120px extra padding at bottom
  }

  List<Widget> _buildDynamicActivityWidgets(
      List<Map<String, String>> activities) {
    if (activities.isEmpty) return [];

    List<Widget> widgets = [];
    double currentPosition = 0;

    // Sort activities by start time
    final sortedActivities = List<Map<String, String>>.from(activities);
    sortedActivities.sort((a, b) {
      final timeA = _parseTimeString(a['startTime'] ?? '09:00');
      final timeB = _parseTimeString(b['startTime'] ?? '09:00');
      return (timeA.hour * 60 + timeA.minute)
          .compareTo(timeB.hour * 60 + timeB.minute);
    });

    for (final activity in sortedActivities) {
      final startTime = _parseTimeString(activity['startTime'] ?? '09:00');
      final endTime = _parseTimeString(activity['endTime'] ?? '10:00');

      // Calculate actual duration in minutes
      final actualDurationMinutes =
          _calculateDurationInMinutes(startTime, endTime);

      // Minimum display height equivalent to 1h 45m (105 pixels)
      const minDisplayHeight = 105.0;

      // Use the larger of actual duration or minimum display height
      final displayHeight = actualDurationMinutes > minDisplayHeight
          ? actualDurationMinutes.toDouble()
          : minDisplayHeight;

      // Calculate position based on actual time using the original time position calculation
      final timePosition = _calculateTimePosition(startTime);

      // Determine final position (avoid overlaps)
      final finalPosition =
          timePosition < currentPosition ? currentPosition : timePosition;

      // Check if this activity has conflicts
      final hasConflict = _hasActivityConflict(activity);

      widgets.add(
        Positioned(
          left: 16,
          top: finalPosition,
          right: 0,
          child: Container(
            height: displayHeight,
            child: _buildActivityCard(activity, hasConflict: hasConflict),
          ),
        ),
      );

      currentPosition = finalPosition + displayHeight + 8; // Add 8px spacing
    }

    return widgets;
  }

  Widget _buildActivityCard(Map<String, String> activity,
      {bool hasConflict = false}) {
    final destination = activity['destination'] ?? '';
    final activityName = activity['name'] ?? '';
    final startTime = activity['startTime'] ?? '09:00';
    final endTime = activity['endTime'] ?? '10:00';
    final day = activity['day'] ?? '1';

    return Draggable<Map<String, String>>(
      data: activity,
      onDragStarted: () {
        setState(() {
          _isDragging = true;
          _draggedActivityKey = '$destination-$activityName-$day';
        });
        HapticFeedback.lightImpact();
      },
      onDragEnd: (details) {
        setState(() {
          _isDragging = false;
          _draggedActivityKey = null;
        });
      },
      feedback: Material(
        elevation: 12,
        borderRadius: BorderRadius.circular(12),
        child: Container(
          width: 280,
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: const Color(0xFF0D76FF).withOpacity(0.15),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: const Color(0xFF0D76FF),
              width: 2,
            ),
            boxShadow: [
              BoxShadow(
                color: const Color(0xFF0D76FF).withOpacity(0.3),
                blurRadius: 20,
                offset: const Offset(0, 8),
              ),
            ],
          ),
          child: _buildActivityContent(
              activityName, destination, startTime, endTime,
              isDragging: true),
        ),
      ),
      childWhenDragging: Opacity(
        opacity: 0.3,
        child: Container(
          margin: const EdgeInsets.only(bottom: 8),
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.grey.withOpacity(0.2),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: const Color(0xFFE2E8F0),
              width: 1,
            ),
          ),
          child: _buildActivityContent(
              activityName, destination, startTime, endTime),
        ),
      ),
      child: DragTarget<Map<String, String>>(
        onAcceptWithDetails: (details) {
          _onActivityDroppedOnActivity(details.data, activity);
        },
        builder: (context, candidateData, rejectedData) {
          final isHovering = candidateData.isNotEmpty;
          return GestureDetector(
            onTap: () => _showDurationEditDialog(
                destination, activityName, startTime, endTime),
            child: Container(
              margin: const EdgeInsets.only(bottom: 8),
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color:
                    hasConflict ? Colors.red.withOpacity(0.05) : Colors.white,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: hasConflict
                      ? Colors.red
                      : isHovering
                          ? const Color(0xFF0D76FF)
                          : const Color(0xFF0D76FF).withOpacity(0.2),
                  width: hasConflict || isHovering ? 2 : 1,
                ),
                boxShadow: [
                  BoxShadow(
                    color: hasConflict
                        ? Colors.red.withOpacity(0.1)
                        : Colors.black.withOpacity(0.05),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Stack(
                children: [
                  _buildActivityContent(
                      activityName, destination, startTime, endTime),
                  if (hasConflict)
                    Positioned(
                      top: 0,
                      right: 0,
                      child: Container(
                        width: 20,
                        height: 20,
                        decoration: const BoxDecoration(
                          color: Colors.red,
                          shape: BoxShape.circle,
                        ),
                        child: const Icon(
                          Icons.warning,
                          size: 12,
                          color: Colors.white,
                        ),
                      ),
                    ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildActivityContent(
      String activityName, String destination, String startTime, String endTime,
      {bool isDragging = false}) {
    // Calculate duration in minutes
    final start = _parseTimeString(startTime);
    final end = _parseTimeString(endTime);
    final durationMinutes = _calculateDurationInMinutes(start, end);

    // Get image for this activity
    final imagePath = _activityImages[destination]?[activityName];
    final hasImage = imagePath != null;

    // Determine layout based on duration (3 hours = 180 minutes)
    final useColumnLayout = durationMinutes >= 180;

    if (useColumnLayout && hasImage) {
      // Column layout for activities ≥ 3 hours with image
      return LayoutBuilder(
        builder: (context, constraints) {
          // Calculate dynamic image height based on available space
          final availableHeight = constraints.maxHeight;
          final textContentHeight = _calculateTextContentHeight();
          final padding = 16.0; // 8px spacing + 8px margins

          // Image takes up remaining space, with minimum of 60px and maximum of 200px
          // Reduce by 10 pixels for better spacing
          final imageHeight =
              (availableHeight - textContentHeight - padding - 10.0)
                  .clamp(60.0, 200.0);

          return SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                // Large dynamic-height image at the top
                Container(
                  width: double.infinity,
                  height: imageHeight,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: const Color(0xFF0D76FF).withOpacity(0.2),
                    ),
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(7),
                    child: Image.file(
                      File(imagePath),
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) {
                        // Log error for debugging
                        print('Error loading activity image: $error');
                        return Container(
                          color: const Color(0xFF718096).withOpacity(0.1),
                          child: const Icon(
                            Icons.broken_image,
                            color: Color(0xFF718096),
                            size: 24,
                          ),
                        );
                      },
                    ),
                  ),
                ),
                const SizedBox(height: 8),
                // Activity details below image
                _buildActivityTextContent(
                    activityName, destination, startTime, endTime, isDragging),
              ],
            ),
          );
        },
      );
    } else if (!useColumnLayout && hasImage) {
      // Row layout for activities < 3 hours with image
      return Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Activity details on the left
          Expanded(
            flex: 2,
            child: _buildActivityTextContent(
                activityName, destination, startTime, endTime, isDragging),
          ),
          const SizedBox(width: 8),
          // Small thumbnail image on the right
          Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: const Color(0xFF0D76FF).withOpacity(0.2),
              ),
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(7),
              child: Image.file(
                File(imagePath),
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) {
                  // Log error for debugging
                  print('Error loading activity thumbnail: $error');
                  return Container(
                    color: const Color(0xFF718096).withOpacity(0.1),
                    child: const Icon(
                      Icons.broken_image,
                      color: Color(0xFF718096),
                      size: 16,
                    ),
                  );
                },
              ),
            ),
          ),
        ],
      );
    } else {
      // No image - use standard layout
      return _buildActivityTextContent(
          activityName, destination, startTime, endTime, isDragging);
    }
  }

  Widget _buildActivityTextContent(String activityName, String destination,
      String startTime, String endTime, bool isDragging) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          activityName,
          style: GoogleFonts.instrumentSans(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: const Color(0xFF2D3748),
          ),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
        const SizedBox(height: 4),
        Row(
          children: [
            const Icon(
              Icons.location_on,
              size: 12,
              color: Color(0xFF718096),
            ),
            const SizedBox(width: 4),
            Expanded(
              child: Text(
                destination,
                style: GoogleFonts.instrumentSans(
                  fontSize: 12,
                  color: const Color(0xFF718096),
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
        const SizedBox(height: 4),
        Row(
          children: [
            const Icon(
              Icons.access_time,
              size: 12,
              color: Color(0xFF0D76FF),
            ),
            const SizedBox(width: 4),
            Text(
              '$startTime - $endTime',
              style: GoogleFonts.instrumentSans(
                fontSize: 12,
                fontWeight: FontWeight.w500,
                color: const Color(0xFF0D76FF),
              ),
            ),
            if (!isDragging) ...[
              const Spacer(),
              // Show image indicator if activity has an image
              if (_activityImages[destination]?[activityName] != null) ...[
                Container(
                  padding: const EdgeInsets.all(2),
                  decoration: BoxDecoration(
                    color: const Color(0xFF0D76FF).withOpacity(0.1),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: const Icon(
                    Icons.image,
                    size: 12,
                    color: Color(0xFF0D76FF),
                  ),
                ),
                const SizedBox(width: 4),
              ],
              // More icon button for additional notes
              GestureDetector(
                onTap: () => _showActivityNotes(destination, activityName),
                child: Container(
                  padding: const EdgeInsets.all(2),
                  decoration: BoxDecoration(
                    color: const Color(0xFF0D76FF).withOpacity(0.1),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: const Icon(
                    Icons.more_horiz,
                    size: 12,
                    color: Color(0xFF0D76FF),
                  ),
                ),
              ),
              const SizedBox(width: 4),
              const Icon(
                Icons.drag_indicator,
                size: 16,
                color: Color(0xFF718096),
              ),
            ],
          ],
        ),
      ],
    );
  }

  Widget _buildDateRangeSection() {
    final duration = _calculateTripDuration();

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            width: 48,
            height: 48,
            decoration: BoxDecoration(
              color: const Color(0xFF0D76FF).withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: const Icon(
              Icons.calendar_today,
              color: Color(0xFF0D76FF),
              size: 24,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  _currentItinerary.dateRange,
                  style: GoogleFonts.instrumentSans(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    color: const Color(0xFF2D3748),
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  duration,
                  style: GoogleFonts.instrumentSans(
                    fontSize: 14,
                    color: const Color(0xFF718096),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  String _calculateTripDuration() {
    try {
      // Parse DD/MM/YYYY format
      final startParts = _currentItinerary.startDate.split('/');
      final endParts = _currentItinerary.endDate.split('/');

      if (startParts.length == 3 && endParts.length == 3) {
        final startDate = DateTime(
          int.parse(startParts[2]), // year
          int.parse(startParts[1]), // month
          int.parse(startParts[0]), // day
        );
        final endDate = DateTime(
          int.parse(endParts[2]), // year
          int.parse(endParts[1]), // month
          int.parse(endParts[0]), // day
        );

        final difference = endDate.difference(startDate).inDays + 1;

        if (difference == 1) {
          return '1 day';
        } else if (difference < 7) {
          return '$difference days';
        } else if (difference == 7) {
          return '1 week';
        } else if (difference < 14) {
          return '1 week, ${difference - 7} days';
        } else if (difference == 14) {
          return '2 weeks';
        } else {
          final weeks = difference ~/ 7;
          final remainingDays = difference % 7;
          if (remainingDays == 0) {
            return '$weeks weeks';
          } else {
            return '$weeks weeks, $remainingDays days';
          }
        }
      }
    } catch (e) {
      // If parsing fails, return a generic message
    }
    return 'Duration not available';
  }

  void _showEditOptions() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.white,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Container(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: const Color(0xFFE2E8F0),
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            const SizedBox(height: 24),
            Text(
              'Itinerary Options',
              style: GoogleFonts.instrumentSans(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: const Color(0xFF2D3748),
              ),
            ),
            const SizedBox(height: 24),
            _buildOptionItem(
              icon: Icons.edit,
              title: 'Edit Itinerary',
              onTap: () {
                Navigator.of(context).pop();
                _navigateToEditScreen();
              },
            ),
            _buildOptionItem(
              icon: Icons.share,
              title: 'Share Itinerary',
              onTap: () {
                Navigator.of(context).pop();
                _shareItinerary();
              },
            ),
            _buildOptionItem(
              icon: Icons.delete,
              title: 'Delete Itinerary',
              isDestructive: true,
              onTap: () {
                Navigator.of(context).pop();
                _showDeleteConfirmation();
              },
            ),
            const SizedBox(height: 24),
          ],
        ),
      ),
    );
  }

  Widget _buildOptionItem({
    required IconData icon,
    required String title,
    required VoidCallback onTap,
    bool isDestructive = false,
  }) {
    return ListTile(
      leading: Icon(
        icon,
        color: isDestructive ? Colors.red : const Color(0xFF0D76FF),
      ),
      title: Text(
        title,
        style: GoogleFonts.instrumentSans(
          fontSize: 16,
          fontWeight: FontWeight.w500,
          color: isDestructive ? Colors.red : const Color(0xFF2D3748),
        ),
      ),
      onTap: onTap,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
    );
  }

  void _showDeleteConfirmation() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'Delete Itinerary',
          style: GoogleFonts.instrumentSans(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: const Color(0xFF2D3748),
          ),
        ),
        content: Text(
          'Are you sure you want to delete "${_currentItinerary.title}"? This action cannot be undone.',
          style: GoogleFonts.instrumentSans(
            fontSize: 16,
            color: const Color(0xFF718096),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(
              'Cancel',
              style: GoogleFonts.instrumentSans(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: const Color(0xFF718096),
              ),
            ),
          ),
          TextButton(
            onPressed: _deleteItinerary,
            child: Text(
              'Delete',
              style: GoogleFonts.instrumentSans(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: Colors.red,
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _navigateToEditScreen() async {
    final result = await Navigator.of(context).push(
      PageRouteBuilder(
        pageBuilder: (context, animation, secondaryAnimation) =>
            EditItineraryScreen(itinerary: _currentItinerary),
        transitionsBuilder: (context, animation, secondaryAnimation, child) {
          const begin = Offset(1.0, 0.0);
          const end = Offset.zero;
          const curve = Curves.easeInOut;

          var tween = Tween(begin: begin, end: end).chain(
            CurveTween(curve: curve),
          );

          return SlideTransition(
            position: animation.drive(tween),
            child: child,
          );
        },
        transitionDuration: const Duration(milliseconds: 300),
      ),
    );

    // If the edit screen returned an updated itinerary, refresh the current screen
    if (result != null && result is Itinerary) {
      setState(() {
        _currentItinerary = result;
        _initializeActivityTimes();
        _initializeActivityImages();
        _initializeTripDays();
      });
    }
  }

  void _deleteItinerary() async {
    Navigator.of(context).pop(); // Close dialog

    setState(() {
      _isLoading = true;
    });

    final success =
        await ItineraryService.deleteItinerary(_currentItinerary.id);

    if (mounted) {
      setState(() {
        _isLoading = false;
      });

      if (success) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Itinerary deleted successfully',
              style: GoogleFonts.instrumentSans(color: Colors.white),
            ),
            backgroundColor: const Color(0xFF0D76FF),
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
          ),
        );
        Navigator.of(context)
            .pop(true); // Return to previous screen with refresh signal
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Failed to delete itinerary. Please try again.',
              style: GoogleFonts.instrumentSans(color: Colors.white),
            ),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
          ),
        );
      }
    }
  }

  Widget _buildDestinationsSection() {
    return Container(
      margin: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(
                Icons.location_on,
                color: Color(0xFF0D76FF),
                size: 24,
              ),
              const SizedBox(width: 8),
              Text(
                'Destinations & Activities',
                style: GoogleFonts.instrumentSans(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: const Color(0xFF2D3748),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          // Destination cards with drag and drop
          Column(
            children:
                _currentItinerary.destinations.asMap().entries.map((entry) {
              final index = entry.key;
              final destination = entry.value;
              final activities =
                  _currentItinerary.dailyActivities[destination] ?? [];

              return DragTarget<int>(
                onAcceptWithDetails: (details) {
                  final draggedIndex = details.data;
                  if (draggedIndex != index) {
                    _onDestinationReorder(draggedIndex, index);
                  }
                },
                builder: (context, candidateData, rejectedData) {
                  final isHovering = candidateData.isNotEmpty;
                  return Container(
                    margin: EdgeInsets.only(
                      bottom: 16,
                      top: isHovering ? 8 : 0,
                    ),
                    decoration: isHovering
                        ? BoxDecoration(
                            border: Border.all(
                              color: const Color(0xFF0D76FF),
                              width: 2,
                            ),
                            borderRadius: BorderRadius.circular(16),
                          )
                        : null,
                    child: _buildDestinationCard(
                      key: ValueKey(destination),
                      destination: destination,
                      activities: activities,
                      index: index,
                    ),
                  );
                },
              );
            }).toList(),
          ),
        ],
      ),
    );
  }

  Widget _buildDestinationCard({
    required Key key,
    required String destination,
    required List<String> activities,
    required int index,
  }) {
    return Draggable<int>(
      data: index,
      feedback: Material(
        elevation: 8,
        borderRadius: BorderRadius.circular(16),
        child: Container(
          width: MediaQuery.of(context).size.width - 32,
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.2),
                blurRadius: 20,
                offset: const Offset(0, 8),
              ),
            ],
          ),
          child: _buildDestinationCardContent(destination, activities, index,
              isDragging: true),
        ),
      ),
      childWhenDragging: Opacity(
        opacity: 0.5,
        child: Container(
          key: key,
          margin: const EdgeInsets.only(bottom: 16),
          decoration: BoxDecoration(
            color: Colors.grey.withOpacity(0.3),
            borderRadius: BorderRadius.circular(16),
            border: Border.all(
              color: const Color(0xFF0D76FF).withOpacity(0.3),
              width: 2,
            ),
          ),
          child: _buildDestinationCardContent(destination, activities, index),
        ),
      ),
      child: Container(
        key: key,
        margin: const EdgeInsets.only(bottom: 16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 10,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: _buildDestinationCardContent(destination, activities, index),
      ),
    );
  }

  Widget _buildDestinationCardContent(
      String destination, List<String> activities, int index,
      {bool isDragging = false}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Destination header with drag handle
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: const Color(0xFF0D76FF).withOpacity(0.05),
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(16),
              topRight: Radius.circular(16),
            ),
          ),
          child: Row(
            children: [
              Container(
                width: 32,
                height: 32,
                decoration: BoxDecoration(
                  color: const Color(0xFF0D76FF),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Center(
                  child: Text(
                    '${index + 1}',
                    style: GoogleFonts.instrumentSans(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  destination,
                  style: GoogleFonts.instrumentSans(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    color: const Color(0xFF2D3748),
                  ),
                ),
              ),
              if (!isDragging)
                const Icon(
                  Icons.drag_handle,
                  color: Color(0xFF718096),
                  size: 20,
                ),
            ],
          ),
        ),

        // Activities section
        if (activities.isNotEmpty)
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Activities',
                  style: GoogleFonts.instrumentSans(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: const Color(0xFF2D3748),
                  ),
                ),
                const SizedBox(height: 12),
                Column(
                  children: activities.asMap().entries.map((entry) {
                    final activityIndex = entry.key;
                    final activity = entry.value;
                    return _buildActivityItem(
                      key: ValueKey('$destination-$activity-$activityIndex'),
                      destination: destination,
                      activity: activity,
                      index: activityIndex,
                    );
                  }).toList(),
                ),
              ],
            ),
          )
        else
          Padding(
            padding: const EdgeInsets.all(16),
            child: Text(
              'No activities planned',
              style: GoogleFonts.instrumentSans(
                fontSize: 14,
                color: const Color(0xFF718096),
                fontStyle: FontStyle.italic,
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildActivityItem({
    required Key key,
    required String destination,
    required String activity,
    required int index,
  }) {
    final activityTime = _activityTimes[destination]?[activity];
    final startTime = activityTime?['startTime'] ?? '09:00';
    final endTime = activityTime?['endTime'] ?? '10:00';

    return Draggable<Map<String, dynamic>>(
      data: {
        'destination': destination,
        'activity': activity,
        'index': index,
      },
      feedback: Material(
        elevation: 8,
        borderRadius: BorderRadius.circular(12),
        child: Container(
          width: MediaQuery.of(context).size.width - 64,
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: const Color(0xFF0D76FF).withOpacity(0.1),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: const Color(0xFF0D76FF),
              width: 2,
            ),
          ),
          child: _buildOldActivityContent(activity, startTime, endTime,
              isDragging: true),
        ),
      ),
      childWhenDragging: Opacity(
        opacity: 0.5,
        child: Container(
          key: key,
          margin: const EdgeInsets.only(bottom: 8),
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.grey.withOpacity(0.3),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: const Color(0xFFE2E8F0),
              width: 1,
            ),
          ),
          child: _buildOldActivityContent(activity, startTime, endTime),
        ),
      ),
      child: DragTarget<Map<String, dynamic>>(
        onAcceptWithDetails: (details) {
          final draggedData = details.data;
          final draggedIndex = draggedData['index'] as int;
          if (draggedIndex != index &&
              draggedData['destination'] == destination) {
            _onActivityReorder(destination, draggedIndex, index);
          }
        },
        builder: (context, candidateData, rejectedData) {
          final isHovering = candidateData.isNotEmpty;
          return GestureDetector(
            onTap: () => _showTimePickerDialog(
                destination, activity, startTime, endTime),
            child: Container(
              key: key,
              margin: const EdgeInsets.only(bottom: 8),
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: const Color(0xFFF7F9FC),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: isHovering
                      ? const Color(0xFF0D76FF)
                      : const Color(0xFFE2E8F0),
                  width: isHovering ? 2 : 1,
                ),
              ),
              child: _buildOldActivityContent(activity, startTime, endTime),
            ),
          );
        },
      ),
    );
  }

  Widget _buildOldActivityContent(
      String activity, String startTime, String endTime,
      {bool isDragging = false}) {
    return Row(
      children: [
        Container(
          width: 6,
          height: 6,
          decoration: const BoxDecoration(
            color: Color(0xFF0D76FF),
            shape: BoxShape.circle,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                activity,
                style: GoogleFonts.instrumentSans(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: const Color(0xFF2D3748),
                ),
              ),
              const SizedBox(height: 4),
              Row(
                children: [
                  const Icon(
                    Icons.access_time,
                    size: 12,
                    color: Color(0xFF718096),
                  ),
                  const SizedBox(width: 4),
                  Text(
                    '$startTime - $endTime',
                    style: GoogleFonts.instrumentSans(
                      fontSize: 12,
                      color: const Color(0xFF718096),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
        if (!isDragging)
          const Icon(
            Icons.drag_handle,
            color: Color(0xFF718096),
            size: 16,
          ),
      ],
    );
  }

  void _showDurationEditDialog(String destination, String activity,
      String currentStartTime, String currentEndTime) {
    final startTime = _parseTimeString(currentStartTime);
    final endTime = _parseTimeString(currentEndTime);
    int durationMinutes = _calculateDurationInMinutes(startTime, endTime);

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setDialogState) {
          // Get current image for this activity (updated in real-time)
          String? currentImagePath = _activityImages[destination]?[activity];

          return AlertDialog(
            title: Text(
              'Edit Activity',
              style: GoogleFonts.instrumentSans(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: const Color(0xFF2D3748),
              ),
            ),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  activity,
                  style: GoogleFonts.instrumentSans(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: const Color(0xFF2D3748),
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 8),
                Text(
                  'Scheduled at $currentStartTime',
                  style: GoogleFonts.instrumentSans(
                    fontSize: 14,
                    color: const Color(0xFF718096),
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 24),

                // Duration Slider
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: const Color(0xFF0D76FF).withOpacity(0.05),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Column(
                    children: [
                      Text(
                        'Duration: ${_formatDuration(durationMinutes)}',
                        style: GoogleFonts.instrumentSans(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: const Color(0xFF2D3748),
                        ),
                      ),
                      const SizedBox(height: 12),
                      Slider(
                        value: durationMinutes.toDouble(),
                        min: 15,
                        max: 480, // 8 hours max
                        divisions: 31, // 15-minute increments
                        activeColor: const Color(0xFF0D76FF),
                        inactiveColor: const Color(0xFF0D76FF).withOpacity(0.2),
                        onChanged: (value) {
                          setDialogState(() {
                            durationMinutes = value.round();
                          });
                        },
                      ),
                      Text(
                        'Drag activities on the timeline to change start time',
                        style: GoogleFonts.instrumentSans(
                          fontSize: 12,
                          color: const Color(0xFF718096),
                          fontStyle: FontStyle.italic,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),

                const SizedBox(height: 16),

                // Image Section
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: const Color(0xFF0D76FF).withOpacity(0.05),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Column(
                    children: [
                      Row(
                        children: [
                          Icon(
                            Icons.image,
                            size: 20,
                            color: const Color(0xFF0D76FF),
                          ),
                          const SizedBox(width: 8),
                          Text(
                            'Activity Image',
                            style: GoogleFonts.instrumentSans(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                              color: const Color(0xFF2D3748),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 12),
                      if (currentImagePath != null)
                        Container(
                          height: 80,
                          width: double.infinity,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(
                              color: const Color(0xFF0D76FF).withOpacity(0.3),
                            ),
                          ),
                          child: ClipRRect(
                            borderRadius: BorderRadius.circular(7),
                            child: Image.file(
                              File(currentImagePath!),
                              fit: BoxFit.cover,
                              errorBuilder: (context, error, stackTrace) {
                                return Container(
                                  color:
                                      const Color(0xFF718096).withOpacity(0.1),
                                  child: const Icon(
                                    Icons.broken_image,
                                    color: Color(0xFF718096),
                                  ),
                                );
                              },
                            ),
                          ),
                        ),
                      const SizedBox(height: 8),
                      SizedBox(
                        width: double.infinity,
                        child: ElevatedButton.icon(
                          onPressed: () => _showImagePickerOptions(
                              destination, activity, setDialogState),
                          icon: Icon(
                            currentImagePath != null
                                ? Icons.edit
                                : Icons.add_a_photo,
                            size: 18,
                          ),
                          label: Text(
                            currentImagePath != null
                                ? 'Change Image'
                                : 'Add Image',
                            style: GoogleFonts.instrumentSans(
                              fontSize: 14,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: const Color(0xFF0D76FF),
                            foregroundColor: Colors.white,
                            elevation: 0,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                            padding: const EdgeInsets.symmetric(vertical: 12),
                          ),
                        ),
                      ),
                      if (currentImagePath != null)
                        Padding(
                          padding: const EdgeInsets.only(top: 8),
                          child: SizedBox(
                            width: double.infinity,
                            child: TextButton.icon(
                              onPressed: () {
                                _removeActivityImage(destination, activity);
                                setDialogState(() {
                                  // This will trigger a rebuild of the dialog without the image
                                });
                              },
                              icon: const Icon(
                                Icons.delete_outline,
                                size: 18,
                                color: Colors.red,
                              ),
                              label: Text(
                                'Remove Image',
                                style: GoogleFonts.instrumentSans(
                                  fontSize: 14,
                                  fontWeight: FontWeight.w600,
                                  color: Colors.red,
                                ),
                              ),
                            ),
                          ),
                        ),
                    ],
                  ),
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: Text(
                  'Cancel',
                  style: GoogleFonts.instrumentSans(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: const Color(0xFF718096),
                  ),
                ),
              ),
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();

                  // Calculate new end time based on current start time and new duration
                  final startMinutes = startTime.hour * 60 + startTime.minute;
                  final endMinutes = startMinutes + durationMinutes;
                  final newEndTime = TimeOfDay(
                    hour: (endMinutes ~/ 60) % 24,
                    minute: endMinutes % 60,
                  );

                  _updateActivityTime(
                      destination, activity, startTime, newEndTime);
                  _saveActivityChanges();
                },
                child: Text(
                  'Save',
                  style: GoogleFonts.instrumentSans(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: const Color(0xFF0D76FF),
                  ),
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  void _showTimePickerDialog(String destination, String activity,
      String currentStartTime, String currentEndTime) {
    TimeOfDay startTime = _parseTimeString(currentStartTime);
    TimeOfDay endTime = _parseTimeString(currentEndTime);
    int durationMinutes = _calculateDurationInMinutes(startTime, endTime);

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setDialogState) => AlertDialog(
          title: Text(
            'Edit Activity Time & Duration',
            style: GoogleFonts.instrumentSans(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: const Color(0xFF2D3748),
            ),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                activity,
                style: GoogleFonts.instrumentSans(
                  fontSize: 16,
                  color: const Color(0xFF718096),
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 24),

              // Duration Slider
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: const Color(0xFF0D76FF).withOpacity(0.05),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Column(
                  children: [
                    Text(
                      'Duration: ${_formatDuration(durationMinutes)}',
                      style: GoogleFonts.instrumentSans(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: const Color(0xFF2D3748),
                      ),
                    ),
                    const SizedBox(height: 12),
                    Slider(
                      value: durationMinutes.toDouble(),
                      min: 15,
                      max: 480, // 8 hours max
                      divisions: 31, // 15-minute increments
                      activeColor: const Color(0xFF0D76FF),
                      inactiveColor: const Color(0xFF0D76FF).withOpacity(0.2),
                      onChanged: (value) {
                        setDialogState(() {
                          durationMinutes = value.round();
                          // Update end time based on new duration
                          final startMinutes =
                              startTime.hour * 60 + startTime.minute;
                          final endMinutes = startMinutes + durationMinutes;
                          endTime = TimeOfDay(
                            hour: (endMinutes ~/ 60) % 24,
                            minute: endMinutes % 60,
                          );
                        });
                      },
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 24),
              Row(
                children: [
                  Expanded(
                    child: Column(
                      children: [
                        Text(
                          'Start Time',
                          style: GoogleFonts.instrumentSans(
                            fontSize: 14,
                            fontWeight: FontWeight.w600,
                            color: const Color(0xFF2D3748),
                          ),
                        ),
                        const SizedBox(height: 8),
                        GestureDetector(
                          onTap: () async {
                            final picked = await showTimePicker(
                              context: context,
                              initialTime: startTime,
                              builder: (context, child) {
                                return Theme(
                                  data: Theme.of(context).copyWith(
                                    colorScheme: const ColorScheme.light(
                                      primary: Color(0xFF0D76FF),
                                    ),
                                  ),
                                  child: child!,
                                );
                              },
                            );
                            if (picked != null) {
                              setDialogState(() {
                                startTime = picked;
                                // Update end time to maintain duration
                                final startMinutes =
                                    picked.hour * 60 + picked.minute;
                                final endMinutes =
                                    startMinutes + durationMinutes;
                                endTime = TimeOfDay(
                                  hour: (endMinutes ~/ 60) % 24,
                                  minute: endMinutes % 60,
                                );
                              });
                            }
                          },
                          child: Container(
                            padding: const EdgeInsets.symmetric(
                                vertical: 12, horizontal: 16),
                            decoration: BoxDecoration(
                              border:
                                  Border.all(color: const Color(0xFF0D76FF)),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Text(
                              startTime.format(context),
                              style: GoogleFonts.instrumentSans(
                                fontSize: 16,
                                color: const Color(0xFF0D76FF),
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      children: [
                        Text(
                          'End Time',
                          style: GoogleFonts.instrumentSans(
                            fontSize: 14,
                            fontWeight: FontWeight.w600,
                            color: const Color(0xFF2D3748),
                          ),
                        ),
                        const SizedBox(height: 8),
                        GestureDetector(
                          onTap: () async {
                            final picked = await showTimePicker(
                              context: context,
                              initialTime: endTime,
                              builder: (context, child) {
                                return Theme(
                                  data: Theme.of(context).copyWith(
                                    colorScheme: const ColorScheme.light(
                                      primary: Color(0xFF0D76FF),
                                    ),
                                  ),
                                  child: child!,
                                );
                              },
                            );
                            if (picked != null) {
                              setDialogState(() {
                                endTime = picked;
                                // Update duration based on new end time
                                durationMinutes = _calculateDurationInMinutes(
                                    startTime, picked);
                              });
                            }
                          },
                          child: Container(
                            padding: const EdgeInsets.symmetric(
                                vertical: 12, horizontal: 16),
                            decoration: BoxDecoration(
                              border:
                                  Border.all(color: const Color(0xFF0D76FF)),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Text(
                              endTime.format(context),
                              style: GoogleFonts.instrumentSans(
                                fontSize: 16,
                                color: const Color(0xFF0D76FF),
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(
                'Cancel',
                style: GoogleFonts.instrumentSans(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: const Color(0xFF718096),
                ),
              ),
            ),
            TextButton(
              onPressed: () {
                _updateActivityTime(destination, activity, startTime, endTime);
                Navigator.of(context).pop();
              },
              child: Text(
                'Save',
                style: GoogleFonts.instrumentSans(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: const Color(0xFF0D76FF),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _updateActivityTime(String destination, String activity,
      TimeOfDay startTime, TimeOfDay endTime,
      {String? day}) {
    final targetDay =
        day ?? _activityTimes[destination]?[activity]?['day'] ?? '1';

    // Check for conflicts before updating
    final conflict = _checkTimeConflict(
        destination, activity, startTime, endTime, targetDay);

    if (conflict != null) {
      _showConflictDialog(conflict, () {
        _forceUpdateActivityTime(
            destination, activity, startTime, endTime, targetDay);
      });
      return;
    }

    _forceUpdateActivityTime(
        destination, activity, startTime, endTime, targetDay);
  }

  void _forceUpdateActivityTime(String destination, String activity,
      TimeOfDay startTime, TimeOfDay endTime, String day) {
    setState(() {
      if (_activityTimes[destination] == null) {
        _activityTimes[destination] = {};
      }
      _activityTimes[destination]![activity] = {
        'startTime':
            '${startTime.hour.toString().padLeft(2, '0')}:${startTime.minute.toString().padLeft(2, '0')}',
        'endTime':
            '${endTime.hour.toString().padLeft(2, '0')}:${endTime.minute.toString().padLeft(2, '0')}',
        'day': day,
      };
    });

    HapticFeedback.lightImpact();
  }

  Map<String, String>? _checkTimeConflict(String destination, String activity,
      TimeOfDay startTime, TimeOfDay endTime, String day) {
    final startMinutes = startTime.hour * 60 + startTime.minute;
    final endMinutes = endTime.hour * 60 + endTime.minute;

    // Check all activities for the same day
    for (final dest in _activityTimes.keys) {
      for (final act in _activityTimes[dest]!.keys) {
        // Skip the activity we're updating
        if (dest == destination && act == activity) continue;

        final activityData = _activityTimes[dest]![act]!;
        final activityDay = activityData['day'] ?? '1';

        // Only check activities on the same day
        if (activityDay != day) continue;

        final actStartTime =
            _parseTimeString(activityData['startTime'] ?? '09:00');
        final actEndTime = _parseTimeString(activityData['endTime'] ?? '10:00');
        final actStartMinutes = actStartTime.hour * 60 + actStartTime.minute;
        final actEndMinutes = actEndTime.hour * 60 + actEndTime.minute;

        // Check for overlap
        if ((startMinutes < actEndMinutes && endMinutes > actStartMinutes)) {
          return {
            'conflictActivity': act,
            'conflictDestination': dest,
            'conflictStartTime': activityData['startTime'] ?? '09:00',
            'conflictEndTime': activityData['endTime'] ?? '10:00',
          };
        }
      }
    }

    return null;
  }

  bool _hasActivityConflict(Map<String, String> activity) {
    final destination = activity['destination'] ?? '';
    final activityName = activity['name'] ?? '';
    final startTime = _parseTimeString(activity['startTime'] ?? '09:00');
    final endTime = _parseTimeString(activity['endTime'] ?? '10:00');
    final day = activity['day'] ?? '1';

    return _checkTimeConflict(
            destination, activityName, startTime, endTime, day) !=
        null;
  }

  void _showConflictDialog(Map<String, String> conflict, VoidCallback onForce) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: const Color(0xFFF7F9FC),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        title: Text(
          'Time Conflict Detected',
          style: GoogleFonts.instrumentSans(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Colors.red,
          ),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.warning_amber_rounded,
              size: 48,
              color: Colors.orange,
            ),
            const SizedBox(height: 16),
            Text(
              'This time slot conflicts with:',
              style: GoogleFonts.instrumentSans(
                fontSize: 16,
                color: const Color(0xFF718096),
              ),
            ),
            const SizedBox(height: 8),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.red.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.red.withOpacity(0.3)),
              ),
              child: Column(
                children: [
                  Text(
                    conflict['conflictActivity'] ?? '',
                    style: GoogleFonts.instrumentSans(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      color: const Color(0xFF2D3748),
                    ),
                  ),
                  Text(
                    '${conflict['conflictStartTime']} - ${conflict['conflictEndTime']}',
                    style: GoogleFonts.instrumentSans(
                      fontSize: 12,
                      color: Colors.red,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(
              'Cancel',
              style: GoogleFonts.instrumentSans(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: const Color(0xFF718096),
              ),
            ),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              onForce();
            },
            child: Text(
              'Override',
              style: GoogleFonts.instrumentSans(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: Colors.red,
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _onActivityDroppedOnTimeSlot(
      Map<String, String> draggedActivity, int hour) {
    // Handle dropping activity on specific time slot
    HapticFeedback.mediumImpact();

    final destination = draggedActivity['destination'] ?? '';
    final activityName = draggedActivity['name'] ?? '';
    final currentDay = (_selectedDayIndex + 1).toString();

    // Get current duration to maintain it
    final currentStartTime =
        _parseTimeString(draggedActivity['startTime'] ?? '09:00');
    final currentEndTime =
        _parseTimeString(draggedActivity['endTime'] ?? '10:00');
    final durationMinutes =
        _calculateDurationInMinutes(currentStartTime, currentEndTime);

    // Set new start time based on dropped hour
    final newStartTime = TimeOfDay(hour: hour, minute: 0);
    final newEndMinutes = (hour * 60) + durationMinutes;
    final newEndTime = TimeOfDay(
      hour: (newEndMinutes ~/ 60) % 24,
      minute: newEndMinutes % 60,
    );

    _updateActivityTime(
      destination,
      activityName,
      newStartTime,
      newEndTime,
      day: currentDay,
    );

    _saveActivityChanges();
  }

  void _onActivityDroppedOnTime(
      Map<String, String> draggedActivity, TimeOfDay time) {
    // Handle dropping activity on specific time (with minutes precision)
    HapticFeedback.mediumImpact();

    final destination = draggedActivity['destination'] ?? '';
    final activityName = draggedActivity['name'] ?? '';
    final currentDay = (_selectedDayIndex + 1).toString();

    // Get current duration to maintain it
    final currentStartTime =
        _parseTimeString(draggedActivity['startTime'] ?? '09:00');
    final currentEndTime =
        _parseTimeString(draggedActivity['endTime'] ?? '10:00');
    final durationMinutes =
        _calculateDurationInMinutes(currentStartTime, currentEndTime);

    // Set new start time based on dropped time
    final newStartTime = time;
    final newEndMinutes = (time.hour * 60 + time.minute) + durationMinutes;
    final newEndTime = TimeOfDay(
      hour: (newEndMinutes ~/ 60) % 24,
      minute: newEndMinutes % 60,
    );

    _updateActivityTime(
      destination,
      activityName,
      newStartTime,
      newEndTime,
      day: currentDay,
    );

    _saveActivityChanges();
  }

  void _onActivityDroppedOnTimeline(Map<String, String> draggedActivity) {
    // Fallback for general timeline drops - use 9 AM as default
    _onActivityDroppedOnTimeSlot(draggedActivity, 9);
  }

  void _onActivityDroppedOnActivity(
      Map<String, String> draggedActivity, Map<String, String> targetActivity) {
    // Handle dropping one activity on another - swap positions or reorder
    HapticFeedback.mediumImpact();

    final draggedDestination = draggedActivity['destination'] ?? '';
    final draggedName = draggedActivity['name'] ?? '';
    final targetDestination = targetActivity['destination'] ?? '';
    final targetName = targetActivity['name'] ?? '';

    // If same destination, swap times
    if (draggedDestination == targetDestination) {
      final draggedStartTime = draggedActivity['startTime'] ?? '09:00';
      final draggedEndTime = draggedActivity['endTime'] ?? '10:00';
      final targetStartTime = targetActivity['startTime'] ?? '09:00';
      final targetEndTime = targetActivity['endTime'] ?? '10:00';

      _updateActivityTime(
        draggedDestination,
        draggedName,
        _parseTimeString(targetStartTime),
        _parseTimeString(targetEndTime),
        day: targetActivity['day'],
      );

      _updateActivityTime(
        targetDestination,
        targetName,
        _parseTimeString(draggedStartTime),
        _parseTimeString(draggedEndTime),
        day: draggedActivity['day'],
      );
    } else {
      // Cross-destination move - move to target's time slot
      _updateActivityTime(
        draggedDestination,
        draggedName,
        _parseTimeString(targetActivity['startTime'] ?? '09:00'),
        _parseTimeString(targetActivity['endTime'] ?? '10:00'),
        day: targetActivity['day'],
      );
    }

    _saveActivityChanges();
  }

  Future<void> _saveActivityChanges() async {
    // Create updated itinerary with current activity times and images
    final updatedItinerary = Itinerary(
      id: _currentItinerary.id,
      title: _currentItinerary.title,
      startDate: _currentItinerary.startDate,
      endDate: _currentItinerary.endDate,
      destinations: _currentItinerary.destinations,
      hasPhoto: _currentItinerary.hasPhoto,
      imagePath: _currentItinerary.imagePath,
      dailyActivities: _currentItinerary.dailyActivities,
      daySpecificActivities: _currentItinerary.daySpecificActivities,
      activityTimes: _activityTimes,
      activityImages: _activityImages,
      accommodation: _currentItinerary.accommodation,
      additionalNotes: _currentItinerary.additionalNotes,
      createdAt: _currentItinerary.createdAt,
    );

    // Update the current itinerary reference
    _currentItinerary = updatedItinerary;

    // Save to storage
    await ItineraryService.updateItinerary(_currentItinerary);
  }

  void _onDestinationReorder(int oldIndex, int newIndex) async {
    HapticFeedback.lightImpact();

    setState(() {
      if (newIndex > oldIndex) {
        newIndex -= 1;
      }

      // Reorder destinations list
      final destinations = List<String>.from(_currentItinerary.destinations);
      final item = destinations.removeAt(oldIndex);
      destinations.insert(newIndex, item);

      // Update the itinerary
      _currentItinerary = Itinerary(
        id: _currentItinerary.id,
        title: _currentItinerary.title,
        startDate: _currentItinerary.startDate,
        endDate: _currentItinerary.endDate,
        destinations: destinations,
        hasPhoto: _currentItinerary.hasPhoto,
        imagePath: _currentItinerary.imagePath,
        dailyActivities: _currentItinerary.dailyActivities,
        accommodation: _currentItinerary.accommodation,
        additionalNotes: _currentItinerary.additionalNotes,
        createdAt: _currentItinerary.createdAt,
      );
    });

    // Save changes to storage
    await ItineraryService.updateItinerary(_currentItinerary);
  }

  void _onActivityReorder(
      String destination, int oldIndex, int newIndex) async {
    HapticFeedback.lightImpact();

    setState(() {
      if (newIndex > oldIndex) {
        newIndex -= 1;
      }

      // Reorder activities for the specific destination
      final activities = List<String>.from(
          _currentItinerary.dailyActivities[destination] ?? []);
      final item = activities.removeAt(oldIndex);
      activities.insert(newIndex, item);

      // Update the daily activities map
      final updatedActivities =
          Map<String, List<String>>.from(_currentItinerary.dailyActivities);
      updatedActivities[destination] = activities;

      // Update the itinerary
      _currentItinerary = Itinerary(
        id: _currentItinerary.id,
        title: _currentItinerary.title,
        startDate: _currentItinerary.startDate,
        endDate: _currentItinerary.endDate,
        destinations: _currentItinerary.destinations,
        hasPhoto: _currentItinerary.hasPhoto,
        imagePath: _currentItinerary.imagePath,
        dailyActivities: updatedActivities,
        accommodation: _currentItinerary.accommodation,
        additionalNotes: _currentItinerary.additionalNotes,
        createdAt: _currentItinerary.createdAt,
      );
    });

    // Save changes to storage
    await ItineraryService.updateItinerary(_currentItinerary);
  }

  Widget _buildAccommodationSection() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                width: 48,
                height: 48,
                decoration: BoxDecoration(
                  color: const Color(0xFF0D76FF).withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Icon(
                  Icons.hotel,
                  color: Color(0xFF0D76FF),
                  size: 24,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Accommodation',
                      style: GoogleFonts.instrumentSans(
                        fontSize: 18,
                        fontWeight: FontWeight.w600,
                        color: const Color(0xFF2D3748),
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      _currentItinerary.accommodation.isNotEmpty
                          ? _currentItinerary.accommodation
                          : 'No accommodation specified',
                      style: GoogleFonts.instrumentSans(
                        fontSize: 14,
                        color: _currentItinerary.accommodation.isNotEmpty
                            ? const Color(0xFF2D3748)
                            : const Color(0xFF718096),
                        fontStyle: _currentItinerary.accommodation.isNotEmpty
                            ? FontStyle.normal
                            : FontStyle.italic,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildAdditionalNotesSection() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                width: 48,
                height: 48,
                decoration: BoxDecoration(
                  color: const Color(0xFF0D76FF).withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Icon(
                  Icons.note_add,
                  color: Color(0xFF0D76FF),
                  size: 24,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Additional Notes',
                      style: GoogleFonts.instrumentSans(
                        fontSize: 18,
                        fontWeight: FontWeight.w600,
                        color: const Color(0xFF2D3748),
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      _currentItinerary.additionalNotes,
                      style: GoogleFonts.instrumentSans(
                        fontSize: 14,
                        color: const Color(0xFF2D3748),
                        height: 1.4,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  void _showImagePickerOptions(
      String destination, String activity, StateSetter setDialogState) {
    HapticFeedback.lightImpact();
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.white,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Container(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: const Color(0xFFE2E8F0),
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            const SizedBox(height: 24),
            Text(
              'Select Photo',
              style: GoogleFonts.instrumentSans(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: const Color(0xFF2D3748),
              ),
            ),
            const SizedBox(height: 24),
            Row(
              children: [
                Expanded(
                  child: _buildImageSourceOption(
                    icon: Icons.camera_alt,
                    label: 'Camera',
                    onTap: () => _pickActivityImage(ImageSource.camera,
                        destination, activity, setDialogState),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildImageSourceOption(
                    icon: Icons.photo_library,
                    label: 'Gallery',
                    onTap: () => _pickActivityImage(ImageSource.gallery,
                        destination, activity, setDialogState),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 24),
          ],
        ),
      ),
    );
  }

  Widget _buildImageSourceOption({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: const Color(0xFF0D76FF).withOpacity(0.05),
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: const Color(0xFF0D76FF).withOpacity(0.2),
          ),
        ),
        child: Column(
          children: [
            Container(
              width: 48,
              height: 48,
              decoration: BoxDecoration(
                color: const Color(0xFF0D76FF),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(
                icon,
                color: Colors.white,
                size: 24,
              ),
            ),
            const SizedBox(height: 12),
            Text(
              label,
              style: GoogleFonts.instrumentSans(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: const Color(0xFF2D3748),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _pickActivityImage(ImageSource source, String destination,
      String activity, StateSetter setDialogState) async {
    Navigator.of(context).pop(); // Close bottom sheet

    try {
      final XFile? image = await _imagePicker.pickImage(
        source: source,
        maxWidth: 1024,
        maxHeight: 1024,
        imageQuality: 85,
      );

      if (image != null) {
        setState(() {
          if (_activityImages[destination] == null) {
            _activityImages[destination] = {};
          }
          _activityImages[destination]![activity] = image.path;
        });

        // Update the dialog state as well to reflect the new image
        setDialogState(() {
          // This will trigger a rebuild of the dialog with the new image
        });

        // Save changes to persistent storage
        _saveActivityChanges();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Failed to pick image. Please try again.',
              style: GoogleFonts.instrumentSans(color: Colors.white),
            ),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
          ),
        );
      }
    }
  }

  void _removeActivityImage(String destination, String activity) {
    setState(() {
      _activityImages[destination]?.remove(activity);
      if (_activityImages[destination]?.isEmpty == true) {
        _activityImages.remove(destination);
      }
    });
    _saveActivityChanges();
  }

  void _showActivityNotes(String destination, String activityName) {
    final currentNotes = _activityNotes[destination]?[activityName] ?? '';

    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.7,
        decoration: const BoxDecoration(
          color: Color(0xFFF7F9FC),
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          children: [
            // Handle bar
            Container(
              margin: const EdgeInsets.only(top: 12),
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: const Color(0xFFE2E8F0),
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            // Header
            Container(
              padding: const EdgeInsets.all(24),
              child: Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Additional Notes',
                          style: GoogleFonts.instrumentSans(
                            fontSize: 24,
                            fontWeight: FontWeight.bold,
                            color: const Color(0xFF2D3748),
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          activityName,
                          style: GoogleFonts.instrumentSans(
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                            color: const Color(0xFF0D76FF),
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ),
                  ),
                  GestureDetector(
                    onTap: () => Navigator.of(context).pop(),
                    child: Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: const Color(0xFF718096).withOpacity(0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: const Icon(
                        Icons.close,
                        size: 20,
                        color: Color(0xFF718096),
                      ),
                    ),
                  ),
                ],
              ),
            ),
            // Content
            Expanded(
              child: Container(
                margin: const EdgeInsets.symmetric(horizontal: 24),
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(16),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.05),
                      blurRadius: 10,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: const Color(0xFF0D76FF).withOpacity(0.1),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: const Icon(
                            Icons.note_alt,
                            size: 20,
                            color: Color(0xFF0D76FF),
                          ),
                        ),
                        const SizedBox(width: 12),
                        Text(
                          'Notes for this activity',
                          style: GoogleFonts.instrumentSans(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                            color: const Color(0xFF2D3748),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    Expanded(
                      child: currentNotes.isEmpty
                          ? Center(
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Container(
                                    padding: const EdgeInsets.all(16),
                                    decoration: BoxDecoration(
                                      color: const Color(0xFF718096)
                                          .withOpacity(0.1),
                                      borderRadius: BorderRadius.circular(12),
                                    ),
                                    child: const Icon(
                                      Icons.note_add,
                                      size: 32,
                                      color: Color(0xFF718096),
                                    ),
                                  ),
                                  const SizedBox(height: 16),
                                  Text(
                                    'No additional notes yet',
                                    style: GoogleFonts.instrumentSans(
                                      fontSize: 16,
                                      fontWeight: FontWeight.w500,
                                      color: const Color(0xFF718096),
                                    ),
                                  ),
                                  const SizedBox(height: 8),
                                  Text(
                                    'Add notes to remember important details about this activity',
                                    style: GoogleFonts.instrumentSans(
                                      fontSize: 14,
                                      color: const Color(0xFF718096),
                                    ),
                                    textAlign: TextAlign.center,
                                  ),
                                ],
                              ),
                            )
                          : SingleChildScrollView(
                              child: Text(
                                currentNotes,
                                style: GoogleFonts.instrumentSans(
                                  fontSize: 16,
                                  height: 1.5,
                                  color: const Color(0xFF2D3748),
                                ),
                              ),
                            ),
                    ),
                  ],
                ),
              ),
            ),
            // Add/Edit button
            Container(
              padding: const EdgeInsets.all(24),
              child: SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: () {
                    Navigator.of(context).pop();
                    _showEditNotesDialog(
                        destination, activityName, currentNotes);
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFF0D76FF),
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    elevation: 0,
                  ),
                  child: Text(
                    currentNotes.isEmpty ? 'Add Notes' : 'Edit Notes',
                    style: GoogleFonts.instrumentSans(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showEditNotesDialog(
      String destination, String activityName, String currentNotes) {
    final TextEditingController notesController =
        TextEditingController(text: currentNotes);

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: const Color(0xFFF7F9FC),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        title: Text(
          'Edit Notes',
          style: GoogleFonts.instrumentSans(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: const Color(0xFF2D3748),
          ),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              activityName,
              style: GoogleFonts.instrumentSans(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: const Color(0xFF0D76FF),
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
            const SizedBox(height: 16),
            TextField(
              controller: notesController,
              maxLines: 6,
              decoration: InputDecoration(
                hintText: 'Add your notes here...',
                hintStyle: GoogleFonts.instrumentSans(
                  color: const Color(0xFF718096),
                ),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: const BorderSide(color: Color(0xFFE2E8F0)),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide:
                      const BorderSide(color: Color(0xFF0D76FF), width: 2),
                ),
                filled: true,
                fillColor: Colors.white,
                contentPadding: const EdgeInsets.all(16),
              ),
              style: GoogleFonts.instrumentSans(
                fontSize: 16,
                color: const Color(0xFF2D3748),
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(
              'Cancel',
              style: GoogleFonts.instrumentSans(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: const Color(0xFF718096),
              ),
            ),
          ),
          ElevatedButton(
            onPressed: () {
              _saveActivityNotes(
                  destination, activityName, notesController.text.trim());
              Navigator.of(context).pop();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF0D76FF),
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              elevation: 0,
            ),
            child: Text(
              'Save',
              style: GoogleFonts.instrumentSans(
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _saveActivityNotes(
      String destination, String activityName, String notes) {
    setState(() {
      if (_activityNotes[destination] == null) {
        _activityNotes[destination] = {};
      }

      if (notes.isEmpty) {
        _activityNotes[destination]!.remove(activityName);
        if (_activityNotes[destination]!.isEmpty) {
          _activityNotes.remove(destination);
        }
      } else {
        _activityNotes[destination]![activityName] = notes;
      }
    });

    // Save to persistent storage (you can extend this to save to your preferred storage)
    _saveActivityChanges();
  }

  void _shareItinerary() async {
    // Show loading dialog
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: const Color(0xFFF7F9FC),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF0D76FF)),
              ),
              const SizedBox(height: 16),
              Text(
                'Sharing Itinerary...',
                style: GoogleFonts.instrumentSans(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: const Color(0xFF2D3748),
                ),
              ),
              const SizedBox(height: 8),
              Text(
                _currentItinerary.activityImages != null &&
                        _currentItinerary.activityImages!.isNotEmpty
                    ? 'Uploading activity images to cloud storage...'
                    : 'Creating collaboration code',
                style: GoogleFonts.instrumentSans(
                  fontSize: 14,
                  color: const Color(0xFF718096),
                ),
              ),
              if (_currentItinerary.activityImages != null &&
                  _currentItinerary.activityImages!.isNotEmpty) ...[
                const SizedBox(height: 12),
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: const Color(0xFF0D76FF).withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Row(
                    children: [
                      const Icon(
                        Icons.cloud_upload,
                        size: 16,
                        color: Color(0xFF0D76FF),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          'Migrating images for collaborative sharing',
                          style: GoogleFonts.instrumentSans(
                            fontSize: 12,
                            color: const Color(0xFF0D76FF),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ],
          ),
        );
      },
    );

    try {
      final collaborativeItinerary =
          await CollaborativeItineraryService.shareItinerary(_currentItinerary);

      if (mounted) {
        Navigator.of(context).pop(); // Close loading dialog

        if (collaborativeItinerary != null) {
          // Show success message with image migration info if applicable
          if (_currentItinerary.activityImages != null &&
              _currentItinerary.activityImages!.isNotEmpty) {
            _showShareSuccessWithMigrationDialog(
                collaborativeItinerary.collaborationCode);
          } else {
            _showShareSuccessDialog(collaborativeItinerary.collaborationCode);
          }
        } else {
          _showShareErrorDialog('Failed to share itinerary. Please try again.');
        }
      }
    } catch (e) {
      if (mounted) {
        Navigator.of(context).pop(); // Close loading dialog
        _showShareErrorDialog(e.toString().replaceAll('Exception: ', ''));
      }
    }
  }

  void _showShareSuccessDialog(String collaborationCode) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: const Color(0xFFF7F9FC),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          title: Row(
            children: [
              Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: const Color(0xFF10B981).withOpacity(0.1),
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  Icons.check,
                  color: Color(0xFF10B981),
                  size: 24,
                ),
              ),
              const SizedBox(width: 12),
              Text(
                'Itinerary Shared!',
                style: GoogleFonts.instrumentSans(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: const Color(0xFF2D3748),
                ),
              ),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'Your itinerary has been shared successfully. Others can join using this code:',
                style: GoogleFonts.instrumentSans(
                  fontSize: 14,
                  color: const Color(0xFF718096),
                  height: 1.4,
                ),
              ),
              const SizedBox(height: 20),
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: const Color(0xFF0D76FF).withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: const Color(0xFF0D76FF).withOpacity(0.3),
                    width: 1,
                  ),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      collaborationCode,
                      style: GoogleFonts.instrumentSans(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: const Color(0xFF0D76FF),
                        letterSpacing: 2,
                      ),
                    ),
                    IconButton(
                      onPressed: () {
                        Clipboard.setData(
                            ClipboardData(text: collaborationCode));
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text(
                              'Code copied to clipboard!',
                              style: GoogleFonts.instrumentSans(
                                  color: Colors.white),
                            ),
                            backgroundColor: const Color(0xFF10B981),
                            behavior: SnackBarBehavior.floating,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(10),
                            ),
                          ),
                        );
                      },
                      icon: const Icon(
                        Icons.copy,
                        color: Color(0xFF0D76FF),
                      ),
                      tooltip: 'Copy code',
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 16),
              Text(
                'Share this code with friends and family so they can join your itinerary!',
                style: GoogleFonts.instrumentSans(
                  fontSize: 12,
                  color: const Color(0xFF718096),
                  height: 1.4,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
          actions: [
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(),
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF0D76FF),
                foregroundColor: Colors.white,
                elevation: 0,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: Text(
                'Done',
                style: GoogleFonts.instrumentSans(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  void _showShareSuccessWithMigrationDialog(String collaborationCode) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: const Color(0xFFF7F9FC),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          title: Row(
            children: [
              Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: const Color(0xFF10B981).withOpacity(0.1),
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  Icons.check,
                  color: Color(0xFF10B981),
                  size: 24,
                ),
              ),
              const SizedBox(width: 12),
              Text(
                'Itinerary Shared!',
                style: GoogleFonts.instrumentSans(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: const Color(0xFF2D3748),
                ),
              ),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'Your itinerary has been shared successfully with all activity images migrated to cloud storage.',
                style: GoogleFonts.instrumentSans(
                  fontSize: 14,
                  color: const Color(0xFF718096),
                  height: 1.4,
                ),
              ),
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: const Color(0xFF10B981).withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    const Icon(
                      Icons.cloud_done,
                      size: 16,
                      color: Color(0xFF10B981),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        'Activity images successfully uploaded to cloud storage',
                        style: GoogleFonts.instrumentSans(
                          fontSize: 12,
                          color: const Color(0xFF10B981),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 16),
              Text(
                'Others can join using this code:',
                style: GoogleFonts.instrumentSans(
                  fontSize: 14,
                  color: const Color(0xFF718096),
                  height: 1.4,
                ),
              ),
              const SizedBox(height: 12),
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: const Color(0xFF0D76FF).withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: const Color(0xFF0D76FF).withOpacity(0.3),
                    width: 1,
                  ),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      collaborationCode,
                      style: GoogleFonts.instrumentSans(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: const Color(0xFF0D76FF),
                        letterSpacing: 2,
                      ),
                    ),
                    IconButton(
                      onPressed: () {
                        Clipboard.setData(
                            ClipboardData(text: collaborationCode));
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text(
                              'Code copied to clipboard!',
                              style: GoogleFonts.instrumentSans(
                                  color: Colors.white),
                            ),
                            backgroundColor: const Color(0xFF10B981),
                            behavior: SnackBarBehavior.floating,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(10),
                            ),
                          ),
                        );
                      },
                      icon: const Icon(
                        Icons.copy,
                        color: Color(0xFF0D76FF),
                      ),
                      tooltip: 'Copy code',
                    ),
                  ],
                ),
              ),
            ],
          ),
          actions: [
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(),
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF0D76FF),
                foregroundColor: Colors.white,
                elevation: 0,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: Text(
                'Done',
                style: GoogleFonts.instrumentSans(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  void _showShareErrorDialog(String error) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: const Color(0xFFF7F9FC),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          title: Row(
            children: [
              Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: Colors.red.withOpacity(0.1),
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  Icons.error_outline,
                  color: Colors.red,
                  size: 24,
                ),
              ),
              const SizedBox(width: 12),
              Text(
                'Share Failed',
                style: GoogleFonts.instrumentSans(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: const Color(0xFF2D3748),
                ),
              ),
            ],
          ),
          content: Text(
            error,
            style: GoogleFonts.instrumentSans(
              fontSize: 14,
              color: const Color(0xFF718096),
              height: 1.4,
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(
                'Cancel',
                style: GoogleFonts.instrumentSans(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: const Color(0xFF718096),
                ),
              ),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                _shareItinerary(); // Retry
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF0D76FF),
                foregroundColor: Colors.white,
                elevation: 0,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: Text(
                'Retry',
                style: GoogleFonts.instrumentSans(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        );
      },
    );
  }
}
