import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:google_generative_ai/google_generative_ai.dart';
import '../utils/network_helper.dart';
import '../services/ai_learning_service.dart';
import '../services/response_optimization_service.dart';
import '../services/learning_configuration_service.dart';

/// Enhanced Gemini service for handling both text conversations and image analysis
class GeminiService {
  static const String _apiKey = 'AIzaSyD-w4gYmoP6TEolElZSJXIWNyMwQIhlOzY';

  // Text model configuration
  static const List<String> _textModelFailoverChain = [
    'gemma-3-27b-it', // Primary text model
    'gemma-3-12b-it', // Fallback 1
    'gemma-3-4b-it', // Fallback 2
  ];

  // Vision model configuration
  static const List<String> _visionModelFailoverChain = [
    'gemma-3-27b-it', // Primary text model
    'gemma-3-12b-it', // Fallback 1
    'gemma-3-4b-it', // Fallback 2
  ];

  static const int _timeoutSeconds = 10;

  // Model instances for text
  static GenerativeModel? _primaryTextModelInstance;
  static GenerativeModel? _fallbackTextModel1Instance;
  static GenerativeModel? _fallbackTextModel2Instance;

  // Model instances for vision
  static GenerativeModel? _primaryVisionModelInstance;
  static GenerativeModel? _fallbackVisionModel1Instance;
  static GenerativeModel? _fallbackVisionModel2Instance;

  // Current model tracking
  static int _currentTextModelIndex = 0;
  static int _currentVisionModelIndex = 0;
  static String? _lastUsedTextModel;
  static String? _lastUsedVisionModel;

  // System prompt for Wanderly AI
  static const String _systemPrompt =
      '''You are Wanderly AI, the world's most knowledgeable and helpful travel assistant. You're designed to make travel planning effortless and exciting for everyone.

🌍 Your Mission:
Help users:
- Discover destinations that fit their vibe (adventurous, romantic, relaxing, etc.)
- Plan how to get there, where to stay, what to do — without stress
- Navigate day-to-day travel moments: getting around, eating well, staying safe
- Find hidden gems, local tips, or even "what's open now"
- Stay calm and confident if things go sideways (missed flights, visa questions, language barriers)

You're the quiet voice in their pocket saying, "Hey, I've got you — let's make this trip amazing."

🎯 Your Style:
- Conversational and warm, like chatting with a well-traveled friend
- Practical and actionable — always include specific next steps
- Culturally aware and respectful
- Optimistic but realistic about challenges
- Quick to offer alternatives when plans change

💡 Your Expertise:
- Real-time travel conditions and requirements
- Budget-friendly and luxury options for every destination
- Cultural nuances, local customs, and etiquette
- Transportation hubs, routes, and logistics
- Food scenes, from street food to fine dining
- Safety considerations and emergency protocols
- Visa requirements, documentation, and border procedures
- Weather patterns and seasonal considerations
- Hidden gems and off-the-beaten-path experiences

Note: don't ask alot of questions, if the user asking an itinerary just come up with a complete itinerary with the user want to go.

IMPORTANT: You are Wanderly AI, created by the Wanderly AI team. Never mention other AI models or companies. Always maintain this identity.''';

  /// Initialize the service
  static Future<void> initialize({bool forceRefresh = false}) async {
    try {
      if (kDebugMode) {
        print('GeminiService: Initializing...');
      }

      _initializeTextModels();
      _initializeVisionModels();

      if (kDebugMode) {
        print('GeminiService: Initialization complete');
      }
    } catch (error) {
      if (kDebugMode) {
        print('GeminiService: Initialization failed: $error');
      }
      rethrow;
    }
  }

  /// Initialize text models
  static void _initializeTextModels() {
    if (_primaryTextModelInstance == null) {
      _primaryTextModelInstance = GenerativeModel(
        model: _textModelFailoverChain[0],
        apiKey: _apiKey,
        generationConfig: GenerationConfig(
          temperature: 0.2,
          topK: 40,
          topP: 0.85,
          maxOutputTokens: 2048,
        ),
      );
    }

    if (_fallbackTextModel1Instance == null) {
      _fallbackTextModel1Instance = GenerativeModel(
        model: _textModelFailoverChain[1],
        apiKey: _apiKey,
        generationConfig: GenerationConfig(
          temperature: 0.2,
          topK: 40,
          topP: 0.85,
          maxOutputTokens: 2048,
        ),
      );
    }

    if (_fallbackTextModel2Instance == null) {
      _fallbackTextModel2Instance = GenerativeModel(
        model: _textModelFailoverChain[2],
        apiKey: _apiKey,
        generationConfig: GenerationConfig(
          temperature: 0.2,
          topK: 40,
          topP: 0.85,
          maxOutputTokens: 2048,
        ),
      );
    }
  }

  /// Initialize vision models
  static void _initializeVisionModels() {
    if (_primaryVisionModelInstance == null) {
      _primaryVisionModelInstance = GenerativeModel(
        model: _visionModelFailoverChain[0],
        apiKey: _apiKey,
        generationConfig: GenerationConfig(
          temperature: 0.3,
          topK: 40,
          topP: 0.95,
          maxOutputTokens: 2048,
        ),
      );
    }

    if (_fallbackVisionModel1Instance == null) {
      _fallbackVisionModel1Instance = GenerativeModel(
        model: _visionModelFailoverChain[1],
        apiKey: _apiKey,
        generationConfig: GenerationConfig(
          temperature: 0.3,
          topK: 40,
          topP: 0.95,
          maxOutputTokens: 2048,
        ),
      );
    }

    if (_fallbackVisionModel2Instance == null) {
      _fallbackVisionModel2Instance = GenerativeModel(
        model: _visionModelFailoverChain[2],
        apiKey: _apiKey,
        generationConfig: GenerationConfig(
          temperature: 0.3,
          topK: 40,
          topP: 0.95,
          maxOutputTokens: 2048,
        ),
      );
    }
  }

  /// Send a chat message and get AI response (text only)
  static Future<String> sendMessage(
      String message, List<Map<String, String>> conversationHistory,
      {String? responseLength}) async {
    // Check network connectivity
    final hasInternet = await NetworkHelper.hasInternetConnection();
    if (!hasInternet) {
      throw Exception(
          'No internet connection available. Please check your network settings and try again.');
    }

    _initializeTextModels();

    // Prepare messages for the API
    final messages = <Content>[];

    // Add system message
    messages.add(Content.text(_systemPrompt));

    // Add conversation history
    for (final msg in conversationHistory) {
      final content = msg['content'] ?? '';
      if (content.isNotEmpty) {
        messages.add(Content.text(content));
      }
    }

    // Add current user message
    messages.add(Content.text(message));

    // Attempt request with text model failover
    return await _attemptTextRequestWithModelFailover(messages,
        responseLength: responseLength);
  }

  /// Attempt API request with model failover for text chat
  static Future<String> _attemptTextRequestWithModelFailover(
      List<Content> messages,
      {String? responseLength}) async {
    final totalModels = _textModelFailoverChain.length;
    final modelErrors = <String, dynamic>{};

    // Try each model in the failover chain
    for (int modelIndex = 0; modelIndex < totalModels; modelIndex++) {
      final currentModel = _textModelFailoverChain[modelIndex];
      _currentTextModelIndex = modelIndex;

      if (kDebugMode) {
        print(
            'GeminiService: Trying text model ${modelIndex + 1}/$totalModels: $currentModel');
      }

      try {
        final response = await _attemptTextRequestWithCurrentModel(messages,
            responseLength: responseLength, modelIndex: modelIndex);

        if (kDebugMode) {
          print('GeminiService: Success with text model: $currentModel');
        }

        // Track response quality for learning
        await _trackResponseQuality(
            messages.last.parts.first.toString(), response);

        return response;
      } catch (error) {
        modelErrors[currentModel] = error;

        if (kDebugMode) {
          print('GeminiService: Text model $currentModel failed: $error');
        }

        // Continue to next model if available
        if (modelIndex < totalModels - 1) {
          continue;
        }
      }
    }

    // If all models failed, throw the last error
    final lastModel = _textModelFailoverChain.last;
    final lastError = modelErrors[lastModel];
    throw Exception(
        'All text models failed. Last error from $lastModel: $lastError');
  }

  /// Attempt text request with current model
  static Future<String> _attemptTextRequestWithCurrentModel(
      List<Content> messages,
      {String? responseLength,
      required int modelIndex}) async {
    GenerativeModel? modelInstance;
    switch (modelIndex) {
      case 0:
        modelInstance = _primaryTextModelInstance;
        break;
      case 1:
        modelInstance = _fallbackTextModel1Instance;
        break;
      case 2:
        modelInstance = _fallbackTextModel2Instance;
        break;
      default:
        throw Exception('Invalid model index: $modelIndex');
    }

    if (modelInstance == null) {
      throw Exception('Model instance not initialized for index: $modelIndex');
    }

    final response = await modelInstance
        .generateContent(messages)
        .timeout(Duration(seconds: _timeoutSeconds));

    final result = response.text;
    if (result != null && result.isNotEmpty) {
      _lastUsedTextModel = _textModelFailoverChain[modelIndex];
      return result;
    } else {
      throw Exception('Empty response from model');
    }
  }

  /// Analyze an image with travel-focused prompts
  static Future<String> analyzeImage(String imagePath, String prompt) async {
    // Check network connectivity
    final hasInternet = await NetworkHelper.hasInternetConnection();
    if (!hasInternet) {
      throw Exception(
          'No internet connection available. Please check your network settings and try again.');
    }

    _initializeVisionModels();

    try {
      // Read image file
      final imageFile = File(imagePath);
      if (!await imageFile.exists()) {
        throw Exception('Image file not found: $imagePath');
      }

      final imageBytes = await imageFile.readAsBytes();

      // Create image part for Gemini
      final imagePart = DataPart('image/jpeg', imageBytes);

      // Enhanced travel-focused prompt
      final enhancedPrompt = '''
$prompt

Please provide a comprehensive analysis focusing on:
1. Location identification (landmarks, cities, countries, regions)
2. Travel-related content (hotels, restaurants, attractions, transportation)
3. Scene description and travel recommendations
4. Cultural or historical significance if applicable
5. Practical travel tips for this location or type of place
6. Best times to visit, local customs, or interesting facts

Be detailed and informative, as this is for travel planning purposes.
''';

      // Attempt request with vision model failover
      return await _attemptVisionRequestWithModelFailover(
          enhancedPrompt, imagePart);
    } catch (e) {
      if (kDebugMode) {
        print('GeminiService: Image analysis failed: $e');
      }

      // Return a helpful error message
      return '''I can see you've shared an image, but I'm currently unable to analyze images directly. However, I'd be happy to help you with travel information if you can describe what you see in the image or tell me the location!

Some things I can help with:
- Travel recommendations for specific destinations
- Information about landmarks and attractions
- Cultural insights and travel tips
- Best times to visit places
- Local customs and etiquette
- Transportation options

Please feel free to describe the image or ask me about any travel-related topics!''';
    }
  }

  /// Attempt API request with model failover for vision analysis
  static Future<String> _attemptVisionRequestWithModelFailover(
      String prompt, DataPart imagePart) async {
    final totalModels = _visionModelFailoverChain.length;
    final modelErrors = <String, dynamic>{};

    // Try each model in the failover chain
    for (int modelIndex = 0; modelIndex < totalModels; modelIndex++) {
      final currentModel = _visionModelFailoverChain[modelIndex];
      _currentVisionModelIndex = modelIndex;

      if (kDebugMode) {
        print(
            'GeminiService: Trying vision model ${modelIndex + 1}/$totalModels: $currentModel');
      }

      try {
        final response = await _attemptVisionRequestWithCurrentModel(
            prompt, imagePart, modelIndex);

        if (kDebugMode) {
          print('GeminiService: Success with vision model: $currentModel');
        }

        return response;
      } catch (error) {
        modelErrors[currentModel] = error;

        if (kDebugMode) {
          print('GeminiService: Vision model $currentModel failed: $error');
        }

        // Continue to next model if available
        if (modelIndex < totalModels - 1) {
          continue;
        }
      }
    }

    // If all models failed, throw the last error
    final lastModel = _visionModelFailoverChain.last;
    final lastError = modelErrors[lastModel];
    throw Exception(
        'All vision models failed. Last error from $lastModel: $lastError');
  }

  /// Attempt vision request with current model
  static Future<String> _attemptVisionRequestWithCurrentModel(
      String prompt, DataPart imagePart, int modelIndex) async {
    GenerativeModel? modelInstance;
    switch (modelIndex) {
      case 0:
        modelInstance = _primaryVisionModelInstance;
        break;
      case 1:
        modelInstance = _fallbackVisionModel1Instance;
        break;
      case 2:
        modelInstance = _fallbackVisionModel2Instance;
        break;
      default:
        throw Exception('Invalid vision model index: $modelIndex');
    }

    if (modelInstance == null) {
      throw Exception(
          'Vision model instance not initialized for index: $modelIndex');
    }

    final response = await modelInstance.generateContent([
      Content.multi([
        TextPart(prompt),
        imagePart,
      ])
    ]).timeout(Duration(seconds: _timeoutSeconds * 2)); // Give vision more time

    final result = response.text;
    if (result != null && result.isNotEmpty) {
      _lastUsedVisionModel = _visionModelFailoverChain[modelIndex];
      return result;
    } else {
      throw Exception('Empty response from vision model');
    }
  }

  /// Track response quality for learning system
  static Future<void> _trackResponseQuality(
      String userQuery, String aiResponse) async {
    try {
      // Check if learning is enabled
      final configService = LearningConfigurationService.instance;
      await configService.initialize();

      if (configService.isLearningAllowed()) {
        // Initialize response optimization service
        await ResponseOptimizationService.instance.initialize();

        // Get historical feedback for context
        await AILearningService.instance.initialize();

        // Analyze response quality (historical feedback would be passed here in full implementation)
        await ResponseOptimizationService.instance.analyzeResponseQuality(
          userQuery,
          aiResponse,
          [], // Historical feedback would be passed here
        );

        if (kDebugMode) {
          print('GeminiService: Response quality analysis completed');
        }
      }
    } catch (error) {
      if (kDebugMode) {
        print('GeminiService: Response quality tracking failed: $error');
      }
      // Don't rethrow - this is optional functionality
    }
  }

  /// Get error message for user display
  static String getErrorMessage(dynamic error) {
    final errorString = error.toString().toLowerCase();

    if (errorString.contains('no internet') ||
        errorString.contains('network') ||
        errorString.contains('connection')) {
      return 'Network connection failed. Please check your internet connection and try again.';
    } else if (errorString.contains('timeout')) {
      return 'Request timed out. Please try again.';
    } else if (errorString.contains('rate limit') ||
        errorString.contains('quota')) {
      return 'Service temporarily unavailable due to high demand. Please try again in a moment.';
    } else if (errorString.contains('authentication') ||
        errorString.contains('unauthorized')) {
      return 'Authentication failed. Please try again later.';
    } else if (errorString.contains('generation stopped')) {
      return 'Response generation was stopped.';
    } else {
      return 'An unexpected error occurred. Please try again.';
    }
  }

  /// Get the current text model being used (for debugging)
  static String getCurrentTextModel() {
    return _textModelFailoverChain[_currentTextModelIndex];
  }

  /// Get the current vision model being used (for debugging)
  static String getCurrentVisionModel() {
    return _visionModelFailoverChain[_currentVisionModelIndex];
  }

  /// Get the last used text model (for debugging)
  static String? getLastUsedTextModel() {
    return _lastUsedTextModel;
  }

  /// Get the last used vision model (for debugging)
  static String? getLastUsedVisionModel() {
    return _lastUsedVisionModel;
  }

  /// Get the text model failover chain
  static List<String> getTextModelFailoverChain() {
    return List.from(_textModelFailoverChain);
  }

  /// Get the vision model failover chain
  static List<String> getVisionModelFailoverChain() {
    return List.from(_visionModelFailoverChain);
  }

  /// Reset text model index to primary model
  static void resetToPrimaryTextModel() {
    _currentTextModelIndex = 0;
  }

  /// Reset vision model index to primary model
  static void resetToPrimaryVisionModel() {
    _currentVisionModelIndex = 0;
  }

  /// Get current text model index
  static int getCurrentTextModelIndex() {
    return _currentTextModelIndex;
  }

  /// Get current vision model index
  static int getCurrentVisionModelIndex() {
    return _currentVisionModelIndex;
  }
}
