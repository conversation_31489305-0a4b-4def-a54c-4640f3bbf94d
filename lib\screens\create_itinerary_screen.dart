import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:image_picker/image_picker.dart';
import '../models/itinerary.dart';
import '../services/itinerary_service.dart';
import '../services/image_storage_service.dart';

class CreateItineraryScreen extends StatefulWidget {
  const CreateItineraryScreen({super.key});

  @override
  State<CreateItineraryScreen> createState() => _CreateItineraryScreenState();
}

class _CreateItineraryScreenState extends State<CreateItineraryScreen>
    with TickerProviderStateMixin {
  late PageController _pageController;
  late AnimationController _fadeController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  int _currentStep = 0;
  final int _totalSteps = 5;

  // Form controllers and state
  final _titleController = TextEditingController();
  final _startDateController = TextEditingController();
  final _endDateController = TextEditingController();
  final List<TextEditingController> _destinationControllers = [
    TextEditingController()
  ];
  final _accommodationController = TextEditingController();
  final _additionalNotesController = TextEditingController();

  bool _multipleCountries = false;
  bool _hasPhoto = false;
  File? _selectedImage;
  final ImagePicker _imagePicker = ImagePicker();

  // Activities controllers - Map of destination to list of activity controllers (legacy)
  Map<String, List<TextEditingController>> _activitiesControllers = {};

  // Day-specific activities controllers - Map of day -> destination -> list of activity controllers
  Map<int, Map<String, List<TextEditingController>>>
      _daySpecificActivitiesControllers = {};

  // Current selected day for activities planning
  int _selectedDay = 1;

  @override
  void initState() {
    super.initState();

    _pageController = PageController();

    // Initialize fade animation
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeInOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0.0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeInOut,
    ));

    // Start animation
    _fadeController.forward();
  }

  @override
  void dispose() {
    _pageController.dispose();
    _fadeController.dispose();
    _titleController.dispose();
    _startDateController.dispose();
    _endDateController.dispose();
    _accommodationController.dispose();
    _additionalNotesController.dispose();
    for (var controller in _destinationControllers) {
      controller.dispose();
    }
    // Dispose activities controllers
    for (var controllerList in _activitiesControllers.values) {
      for (var controller in controllerList) {
        controller.dispose();
      }
    }
    // Dispose day-specific activities controllers
    for (var dayControllers in _daySpecificActivitiesControllers.values) {
      for (var controllerList in dayControllers.values) {
        for (var controller in controllerList) {
          controller.dispose();
        }
      }
    }
    super.dispose();
  }

  void _nextStep() async {
    HapticFeedback.lightImpact();

    // Check if current step can proceed
    if (!_canProceedFromStep(_currentStep)) {
      _showValidationError();
      return;
    }

    if (_currentStep < _totalSteps - 1) {
      await _fadeController.reverse();
      setState(() {
        _currentStep++;
      });
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
      await _fadeController.forward();
    }
  }

  void _showValidationError() {
    String errorMessage = '';

    switch (_currentStep) {
      case 3: // Activities step
        errorMessage =
            'Please add at least one activity for your destinations.';
        break;
      case 4: // Accommodation step
        errorMessage = 'Please provide accommodation information.';
        break;
      default:
        errorMessage = 'Please fill in all required fields.';
    }

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          errorMessage,
          style: GoogleFonts.instrumentSans(color: Colors.white),
        ),
        backgroundColor: Colors.red,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
        duration: const Duration(seconds: 3),
      ),
    );
  }

  void _previousStep() async {
    HapticFeedback.lightImpact();

    if (_currentStep > 0) {
      await _fadeController.reverse();
      setState(() {
        _currentStep--;
      });
      _pageController.previousPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
      await _fadeController.forward();
    }
  }

  void _addDestination() {
    setState(() {
      _destinationControllers.add(TextEditingController());
    });
  }

  void _removeDestination(int index) {
    if (_destinationControllers.length > 1) {
      setState(() {
        _destinationControllers[index].dispose();
        _destinationControllers.removeAt(index);
      });
    }
  }

  bool _canProceedFromStep(int step) {
    switch (step) {
      case 0:
        return _titleController.text.trim().isNotEmpty &&
            _startDateController.text.trim().isNotEmpty &&
            _endDateController.text.trim().isNotEmpty;
      case 1:
        return true; // Photo is optional
      case 2:
        return _destinationControllers
            .any((controller) => controller.text.trim().isNotEmpty);
      case 3:
        // Activities are now mandatory - check if at least one activity is filled for any day/destination
        final destinations = _destinationControllers
            .map((controller) => controller.text.trim())
            .where((text) => text.isNotEmpty)
            .toList();

        final totalDays = _getTotalDays();

        for (int day = 1; day <= totalDays; day++) {
          if (_daySpecificActivitiesControllers.containsKey(day)) {
            for (final destination in destinations) {
              if (_daySpecificActivitiesControllers[day]!
                  .containsKey(destination)) {
                final hasActivity =
                    _daySpecificActivitiesControllers[day]![destination]!
                        .any((controller) => controller.text.trim().isNotEmpty);
                if (hasActivity) return true;
              }
            }
          }
        }
        return false;
      case 4:
        // Accommodation is now mandatory
        return _accommodationController.text.trim().isNotEmpty;
      default:
        return false;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF7F9FC),
      appBar: AppBar(
        backgroundColor: const Color(0xFFF7F9FC),
        elevation: 0,
        leading: IconButton(
          onPressed: () => Navigator.of(context).pop(),
          icon: const Icon(
            Icons.arrow_back_ios,
            color: Color(0xFF2D3748),
          ),
        ),
        title: Text(
          'Create Itinerary',
          style: GoogleFonts.instrumentSans(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: const Color(0xFF2D3748),
          ),
        ),
        centerTitle: true,
      ),
      body: SafeArea(
        child: Column(
          children: [
            // Progress Indicator
            _buildProgressIndicator(),

            // Page Content
            Expanded(
              child: PageView(
                controller: _pageController,
                physics: const NeverScrollableScrollPhysics(),
                children: [
                  _buildScrollableStep(_buildBasicInfoStep()),
                  _buildScrollableStep(_buildPhotoStep()),
                  _buildScrollableStep(_buildDestinationStep()),
                  _buildScrollableStep(_buildActivitiesStep()),
                  _buildScrollableStep(_buildAccommodationStep()),
                ],
              ),
            ),

            // Navigation Buttons
            _buildNavigationButtons(),
          ],
        ),
      ),
    );
  }

  Widget _buildProgressIndicator() {
    return Container(
      padding: const EdgeInsets.all(24),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            '${_currentStep + 1}',
            style: GoogleFonts.instrumentSans(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: const Color(0xFF0D76FF),
            ),
          ),
          Text(
            ' / $_totalSteps',
            style: GoogleFonts.instrumentSans(
              fontSize: 18,
              fontWeight: FontWeight.w500,
              color: const Color(0xFF718096),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildScrollableStep(Widget stepContent) {
    return SingleChildScrollView(
      physics: const BouncingScrollPhysics(),
      child: stepContent,
    );
  }

  Widget _buildBasicInfoStep() {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: SlideTransition(
        position: _slideAnimation,
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              const SizedBox(height: 40),
              Text(
                'Let\'s plan your trip',
                style: GoogleFonts.instrumentSans(
                  fontSize: 32,
                  fontWeight: FontWeight.bold,
                  color: const Color(0xFF2D3748),
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'Tell us the basics about your upcoming adventure',
                style: GoogleFonts.instrumentSans(
                  fontSize: 16,
                  color: const Color(0xFF718096),
                  height: 1.4,
                ),
              ),
              const SizedBox(height: 48),
              _buildFormField(
                label: 'Trip Title',
                controller: _titleController,
                hint: 'e.g., Summer Vacation in Bali',
                icon: Icons.title,
                onChanged: (value) {
                  setState(
                      () {}); // Trigger rebuild to update Next button state
                },
              ),
              const SizedBox(height: 24),
              Row(
                children: [
                  Expanded(
                    child: _buildDateField(
                      label: 'Start Date',
                      controller: _startDateController,
                      hint: 'Select start date',
                      isStartDate: true,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: _buildDateField(
                      label: 'End Date',
                      controller: _endDateController,
                      hint: 'Select end date',
                      isStartDate: false,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 40), // Extra bottom padding
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPhotoStep() {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: SlideTransition(
        position: _slideAnimation,
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              const SizedBox(height: 40),
              Text(
                'Add a cover photo',
                style: GoogleFonts.instrumentSans(
                  fontSize: 32,
                  fontWeight: FontWeight.bold,
                  color: const Color(0xFF2D3748),
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'Make your itinerary stand out with a beautiful cover image',
                style: GoogleFonts.instrumentSans(
                  fontSize: 16,
                  color: const Color(0xFF718096),
                  height: 1.4,
                ),
              ),
              const SizedBox(height: 48),

              // Photo placeholder
              GestureDetector(
                onTap: _showImagePickerOptions,
                child: Container(
                  width: double.infinity,
                  height: 200,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(
                      color: _selectedImage != null
                          ? const Color(0xFF0D76FF)
                          : const Color(0xFFE2E8F0),
                      width: 2,
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.05),
                        blurRadius: 10,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: _selectedImage != null
                      ? ClipRRect(
                          borderRadius: BorderRadius.circular(14),
                          child: Image.file(
                            _selectedImage!,
                            fit: BoxFit.cover,
                            width: double.infinity,
                            height: double.infinity,
                          ),
                        )
                      : const Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.add_a_photo,
                              size: 48,
                              color: Color(0xFF718096),
                            ),
                            SizedBox(height: 16),
                            Text(
                              'Tap to add photo',
                              style: TextStyle(
                                fontSize: 16,
                                color: Color(0xFF718096),
                              ),
                            ),
                          ],
                        ),
                ),
              ),
              const SizedBox(height: 32),

              // Photo Action Button
              SizedBox(
                width: double.infinity,
                height: 56,
                child: ElevatedButton(
                  onPressed: _showImagePickerOptions,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFF0D76FF),
                    foregroundColor: Colors.white,
                    elevation: 0,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(16),
                    ),
                  ),
                  child: Text(
                    _selectedImage != null ? 'Change Photo' : 'Add Photo',
                    style: GoogleFonts.instrumentSans(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ),
              const SizedBox(height: 40), // Extra bottom padding
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDestinationStep() {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: SlideTransition(
        position: _slideAnimation,
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              const SizedBox(height: 40),
              Text(
                'Where are you going?',
                style: GoogleFonts.instrumentSans(
                  fontSize: 32,
                  fontWeight: FontWeight.bold,
                  color: const Color(0xFF2D3748),
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'Add your destinations for this amazing trip',
                style: GoogleFonts.instrumentSans(
                  fontSize: 16,
                  color: const Color(0xFF718096),
                  height: 1.4,
                ),
              ),
              const SizedBox(height: 32),

              // Multiple countries checkbox
              Row(
                children: [
                  Checkbox(
                    value: _multipleCountries,
                    onChanged: (value) {
                      HapticFeedback.lightImpact();
                      setState(() {
                        _multipleCountries = value ?? false;
                        if (!_multipleCountries &&
                            _destinationControllers.length > 1) {
                          // Remove extra controllers when switching to single country
                          for (int i = _destinationControllers.length - 1;
                              i > 0;
                              i--) {
                            _destinationControllers[i].dispose();
                            _destinationControllers.removeAt(i);
                          }
                        }
                      });
                    },
                    activeColor: const Color(0xFF0D76FF),
                  ),
                  Text(
                    'Multiple countries',
                    style: GoogleFonts.instrumentSans(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                      color: const Color(0xFF2D3748),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 24),

              // Destination inputs
              ...List.generate(
                _multipleCountries ? _destinationControllers.length : 1,
                (index) {
                  final controller = _destinationControllers[index];
                  final isLast = index == _destinationControllers.length - 1;
                  final canAddMore = _multipleCountries &&
                      controller.text.trim().isNotEmpty &&
                      isLast;

                  return Column(
                    children: [
                      Row(
                        children: [
                          Expanded(
                            child: _buildFormField(
                              label: _multipleCountries
                                  ? 'Destination ${index + 1}'
                                  : 'Destination',
                              controller: controller,
                              hint: 'e.g., Bali, Indonesia',
                              icon: Icons.location_on,
                              onChanged: (value) {
                                setState(
                                    () {}); // Trigger rebuild to show/hide add button
                              },
                            ),
                          ),
                          if (_multipleCountries &&
                              _destinationControllers.length > 1)
                            IconButton(
                              onPressed: () {
                                HapticFeedback.lightImpact();
                                _removeDestination(index);
                              },
                              icon: const Icon(
                                Icons.remove_circle,
                                color: Colors.red,
                              ),
                            ),
                        ],
                      ),
                      if (canAddMore) ...[
                        const SizedBox(height: 16),
                        SizedBox(
                          width: double.infinity,
                          child: OutlinedButton.icon(
                            onPressed: () {
                              HapticFeedback.lightImpact();
                              _addDestination();
                            },
                            icon: const Icon(
                              Icons.add,
                              color: Color(0xFF0D76FF),
                            ),
                            label: Text(
                              'Add Another Destination',
                              style: GoogleFonts.instrumentSans(
                                fontSize: 16,
                                fontWeight: FontWeight.w600,
                                color: const Color(0xFF0D76FF),
                              ),
                            ),
                            style: OutlinedButton.styleFrom(
                              side: const BorderSide(
                                color: Color(0xFF0D76FF),
                                width: 2,
                              ),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(16),
                              ),
                              padding: const EdgeInsets.symmetric(vertical: 16),
                            ),
                          ),
                        ),
                      ],
                      const SizedBox(height: 16),
                    ],
                  );
                },
              ),
              const SizedBox(height: 40), // Extra bottom padding
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildNavigationButtons() {
    return Container(
      padding: const EdgeInsets.all(24),
      child: Row(
        children: [
          if (_currentStep > 0)
            Expanded(
              child: OutlinedButton(
                onPressed: _previousStep,
                style: OutlinedButton.styleFrom(
                  side: const BorderSide(
                    color: Color(0xFF718096),
                    width: 2,
                  ),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(16),
                  ),
                  padding: const EdgeInsets.symmetric(vertical: 16),
                ),
                child: Text(
                  'Back',
                  style: GoogleFonts.instrumentSans(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: const Color(0xFF718096),
                  ),
                ),
              ),
            ),
          if (_currentStep > 0) const SizedBox(width: 16),
          Expanded(
            flex: _currentStep == 0 ? 1 : 1,
            child: ElevatedButton(
              onPressed: _canProceedFromStep(_currentStep)
                  ? (_currentStep == _totalSteps - 1
                      ? _createItinerary
                      : _nextStep)
                  : null,
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF0D76FF),
                foregroundColor: Colors.white,
                elevation: 0,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(16),
                ),
                padding: const EdgeInsets.symmetric(vertical: 16),
              ),
              child: Text(
                _currentStep == _totalSteps - 1 ? 'Create Itinerary' : 'Next',
                style: GoogleFonts.instrumentSans(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _showImagePickerOptions() {
    HapticFeedback.lightImpact();
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.white,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Container(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: const Color(0xFFE2E8F0),
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            const SizedBox(height: 24),
            Text(
              'Select Photo',
              style: GoogleFonts.instrumentSans(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: const Color(0xFF2D3748),
              ),
            ),
            const SizedBox(height: 24),
            Row(
              children: [
                Expanded(
                  child: _buildImageSourceOption(
                    icon: Icons.camera_alt,
                    label: 'Camera',
                    onTap: () => _pickImage(ImageSource.camera),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildImageSourceOption(
                    icon: Icons.photo_library,
                    label: 'Gallery',
                    onTap: () => _pickImage(ImageSource.gallery),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 24),
          ],
        ),
      ),
    );
  }

  Widget _buildImageSourceOption({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: const Color(0xFFF7F9FC),
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: const Color(0xFFE2E8F0),
            width: 1,
          ),
        ),
        child: Column(
          children: [
            Icon(
              icon,
              size: 32,
              color: const Color(0xFF0D76FF),
            ),
            const SizedBox(height: 8),
            Text(
              label,
              style: GoogleFonts.instrumentSans(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: const Color(0xFF2D3748),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _pickImage(ImageSource source) async {
    Navigator.of(context).pop(); // Close bottom sheet

    try {
      final XFile? image = await _imagePicker.pickImage(
        source: source,
        maxWidth: 1024,
        maxHeight: 1024,
        imageQuality: 85,
      );

      if (image != null) {
        setState(() {
          _selectedImage = File(image.path);
          _hasPhoto = true;
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Failed to pick image. Please try again.',
              style: GoogleFonts.instrumentSans(color: Colors.white),
            ),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
          ),
        );
      }
    }
  }

  int _getTotalDays() {
    if (_startDateController.text.isEmpty || _endDateController.text.isEmpty) {
      return 1;
    }

    try {
      final startParts = _startDateController.text.split('/');
      final endParts = _endDateController.text.split('/');

      if (startParts.length == 3 && endParts.length == 3) {
        final startDate = DateTime(
          int.parse(startParts[2]), // year
          int.parse(startParts[1]), // month
          int.parse(startParts[0]), // day
        );
        final endDate = DateTime(
          int.parse(endParts[2]), // year
          int.parse(endParts[1]), // month
          int.parse(endParts[0]), // day
        );

        return endDate.difference(startDate).inDays + 1;
      }
    } catch (e) {
      // If parsing fails, return 1
    }
    return 1;
  }

  Widget _buildActivitiesStep() {
    // Get filled destinations
    final destinations = _destinationControllers
        .map((controller) => controller.text.trim())
        .where((text) => text.isNotEmpty)
        .toList();

    final totalDays = _getTotalDays();

    // Ensure selected day is within valid range
    if (_selectedDay > totalDays) {
      _selectedDay = 1;
    }

    // Initialize day-specific activities controllers for each day and destination
    for (int day = 1; day <= totalDays; day++) {
      if (!_daySpecificActivitiesControllers.containsKey(day)) {
        _daySpecificActivitiesControllers[day] = {};
      }

      for (final destination in destinations) {
        if (!_daySpecificActivitiesControllers[day]!.containsKey(destination)) {
          _daySpecificActivitiesControllers[day]![destination] = [
            TextEditingController()
          ];
        }
      }
    }

    return FadeTransition(
      opacity: _fadeAnimation,
      child: SlideTransition(
        position: _slideAnimation,
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              const SizedBox(height: 40),
              Text(
                'Plan your daily activities',
                style: GoogleFonts.instrumentSans(
                  fontSize: 32,
                  fontWeight: FontWeight.bold,
                  color: const Color(0xFF2D3748),
                ),
              ),
              const SizedBox(height: 8),
              RichText(
                text: TextSpan(
                  style: GoogleFonts.instrumentSans(
                    fontSize: 16,
                    color: const Color(0xFF718096),
                    height: 1.4,
                  ),
                  children: [
                    const TextSpan(
                        text:
                            'What would you like to do in each destination? '),
                    TextSpan(
                      text: '*Required',
                      style: GoogleFonts.instrumentSans(
                        fontSize: 16,
                        color: Colors.red,
                        fontWeight: FontWeight.w600,
                        height: 1.4,
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 32),

              // Day selector
              if (totalDays > 1) ...[
                Text(
                  'Select Day',
                  style: GoogleFonts.instrumentSans(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    color: const Color(0xFF2D3748),
                  ),
                ),
                const SizedBox(height: 16),
                Container(
                  height: 60,
                  child: ListView.builder(
                    scrollDirection: Axis.horizontal,
                    itemCount: totalDays,
                    itemBuilder: (context, index) {
                      final day = index + 1;
                      final isSelected = day == _selectedDay;

                      return GestureDetector(
                        onTap: () {
                          setState(() {
                            _selectedDay = day;
                          });
                        },
                        child: Container(
                          width: 80,
                          margin: const EdgeInsets.only(right: 12),
                          decoration: BoxDecoration(
                            color: isSelected
                                ? const Color(0xFF0D76FF)
                                : Colors.white,
                            borderRadius: BorderRadius.circular(12),
                            border: Border.all(
                              color: isSelected
                                  ? const Color(0xFF0D76FF)
                                  : const Color(0xFFE2E8F0),
                              width: 2,
                            ),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withOpacity(0.05),
                                blurRadius: 8,
                                offset: const Offset(0, 2),
                              ),
                            ],
                          ),
                          child: Center(
                            child: Text(
                              'Day $day',
                              style: GoogleFonts.instrumentSans(
                                fontSize: 14,
                                fontWeight: FontWeight.w600,
                                color: isSelected
                                    ? Colors.white
                                    : const Color(0xFF2D3748),
                              ),
                            ),
                          ),
                        ),
                      );
                    },
                  ),
                ),
                const SizedBox(height: 32),
              ],

              // Activities for each destination on selected day
              if (destinations.isEmpty)
                Center(
                  child: Padding(
                    padding: const EdgeInsets.all(40.0),
                    child: Text(
                      'Please add destinations first',
                      style: GoogleFonts.instrumentSans(
                        fontSize: 16,
                        color: const Color(0xFF718096),
                      ),
                    ),
                  ),
                )
              else
                ...destinations.asMap().entries.map((destEntry) {
                  final destIndex = destEntry.key;
                  final destination = destEntry.value;
                  final controllers = _daySpecificActivitiesControllers[
                      _selectedDay]![destination]!;

                  return Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        totalDays > 1
                            ? 'What would you like to do in $destination on Day $_selectedDay?'
                            : 'What would you like to do in $destination?',
                        style: GoogleFonts.instrumentSans(
                          fontSize: 18,
                          fontWeight: FontWeight.w600,
                          color: const Color(0xFF2D3748),
                        ),
                      ),
                      const SizedBox(height: 16),

                      // Activity inputs for this destination
                      ...controllers.asMap().entries.map((entry) {
                        final activityIndex = entry.key;
                        final controller = entry.value;
                        final isLast = activityIndex == controllers.length - 1;
                        final canAddMore =
                            controller.text.trim().isNotEmpty && isLast;

                        return Column(
                          children: [
                            Row(
                              children: [
                                Expanded(
                                  child: _buildFormField(
                                    label: 'Activity ${activityIndex + 1}',
                                    controller: controller,
                                    hint:
                                        'e.g., Visit temples, Beach day, Shopping',
                                    icon: Icons.local_activity,
                                    onChanged: (value) {
                                      setState(() {}); // Trigger rebuild
                                    },
                                  ),
                                ),
                                if (controllers.length > 1)
                                  IconButton(
                                    onPressed: () {
                                      HapticFeedback.lightImpact();
                                      setState(() {
                                        controllers[activityIndex].dispose();
                                        controllers.removeAt(activityIndex);
                                      });
                                    },
                                    icon: const Icon(
                                      Icons.remove_circle,
                                      color: Colors.red,
                                    ),
                                  ),
                              ],
                            ),
                            if (canAddMore) ...[
                              const SizedBox(height: 12),
                              SizedBox(
                                width: double.infinity,
                                child: OutlinedButton.icon(
                                  onPressed: () {
                                    HapticFeedback.lightImpact();
                                    setState(() {
                                      controllers.add(TextEditingController());
                                    });
                                  },
                                  icon: const Icon(
                                    Icons.add,
                                    color: Color(0xFF0D76FF),
                                  ),
                                  label: Text(
                                    'Add Another Activity',
                                    style: GoogleFonts.instrumentSans(
                                      fontSize: 14,
                                      fontWeight: FontWeight.w600,
                                      color: const Color(0xFF0D76FF),
                                    ),
                                  ),
                                  style: OutlinedButton.styleFrom(
                                    side: const BorderSide(
                                      color: Color(0xFF0D76FF),
                                      width: 1,
                                    ),
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(12),
                                    ),
                                    padding: const EdgeInsets.symmetric(
                                        vertical: 12),
                                  ),
                                ),
                              ),
                            ],
                            const SizedBox(height: 16),
                          ],
                        );
                      }),

                      if (destIndex < destinations.length - 1) ...[
                        const Divider(height: 32),
                        const SizedBox(height: 16),
                      ],
                    ],
                  );
                }),
              const SizedBox(height: 40), // Extra bottom padding
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAccommodationStep() {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: SlideTransition(
        position: _slideAnimation,
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              const SizedBox(height: 40),
              Text(
                'Where will you stay?',
                style: GoogleFonts.instrumentSans(
                  fontSize: 32,
                  fontWeight: FontWeight.bold,
                  color: const Color(0xFF2D3748),
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'Tell us about your accommodation and any additional notes',
                style: GoogleFonts.instrumentSans(
                  fontSize: 16,
                  color: const Color(0xFF718096),
                  height: 1.4,
                ),
              ),
              const SizedBox(height: 48),
              _buildFormField(
                label: 'Accommodation *',
                controller: _accommodationController,
                hint: 'e.g., Hotel Bali Paradise, Airbnb in Tokyo',
                icon: Icons.hotel,
                isRequired: true,
                onChanged: (value) {
                  setState(() {}); // Trigger rebuild
                },
              ),
              const SizedBox(height: 24),
              _buildFormField(
                label: 'Additional Notes',
                controller: _additionalNotesController,
                hint: 'Any special requirements, preferences, or reminders',
                icon: Icons.note_add,
                maxLines: 4,
                onChanged: (value) {
                  setState(() {}); // Trigger rebuild
                },
              ),
              const SizedBox(height: 40), // Extra bottom padding
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildFormField({
    required String label,
    required TextEditingController controller,
    required String hint,
    required IconData icon,
    Function(String)? onChanged,
    int maxLines = 1,
    bool isRequired = false,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        RichText(
          text: TextSpan(
            style: GoogleFonts.instrumentSans(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: const Color(0xFF2D3748),
            ),
            children: [
              TextSpan(text: label.replaceAll(' *', '')),
              if (isRequired || label.contains('*'))
                TextSpan(
                  text: ' *',
                  style: GoogleFonts.instrumentSans(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Colors.red,
                  ),
                ),
            ],
          ),
        ),
        const SizedBox(height: 8),
        Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            border: isRequired && controller.text.trim().isEmpty
                ? Border.all(color: Colors.red.withOpacity(0.3), width: 1)
                : null,
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.05),
                blurRadius: 10,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: TextField(
            controller: controller,
            onChanged: onChanged,
            maxLines: maxLines,
            style: GoogleFonts.instrumentSans(
              fontSize: 16,
              color: const Color(0xFF2D3748),
            ),
            decoration: InputDecoration(
              hintText: hint,
              hintStyle: GoogleFonts.instrumentSans(
                fontSize: 16,
                color: const Color(0xFF718096),
              ),
              prefixIcon: Icon(
                icon,
                color: const Color(0xFF0D76FF),
              ),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide.none,
              ),
              filled: true,
              fillColor: Colors.white,
              contentPadding: const EdgeInsets.symmetric(
                horizontal: 16,
                vertical: 16,
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildDateField({
    required String label,
    required TextEditingController controller,
    required String hint,
    required bool isStartDate,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: GoogleFonts.instrumentSans(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: const Color(0xFF2D3748),
          ),
        ),
        const SizedBox(height: 8),
        Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.05),
                blurRadius: 10,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Material(
            color: Colors.transparent,
            child: InkWell(
              onTap: () => _selectDate(isStartDate),
              borderRadius: BorderRadius.circular(12),
              child: Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 16,
                ),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Row(
                  children: [
                    const Icon(
                      Icons.calendar_today,
                      color: Color(0xFF0D76FF),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        controller.text.isEmpty ? hint : controller.text,
                        style: GoogleFonts.instrumentSans(
                          fontSize: 16,
                          color: controller.text.isEmpty
                              ? const Color(0xFF718096)
                              : const Color(0xFF2D3748),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Future<void> _selectDate(bool isStartDate) async {
    HapticFeedback.lightImpact();

    DateTime initialDate = DateTime.now();
    DateTime firstDate = DateTime.now();
    DateTime lastDate = DateTime.now().add(const Duration(days: 365 * 2));

    // If selecting end date and start date is already selected,
    // set initial date to start date or later
    if (!isStartDate && _startDateController.text.isNotEmpty) {
      try {
        // Parse DD/MM/YYYY format
        final dateParts = _startDateController.text.split('/');
        if (dateParts.length == 3) {
          final startDate = DateTime(
            int.parse(dateParts[2]), // year
            int.parse(dateParts[1]), // month
            int.parse(dateParts[0]), // day
          );
          initialDate = startDate.add(const Duration(days: 1));
          firstDate = startDate;
        }
      } catch (e) {
        // If parsing fails, use default dates
      }
    }

    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: initialDate,
      firstDate: firstDate,
      lastDate: lastDate,
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: const ColorScheme.light(
              primary: Color(0xFF0D76FF),
              onPrimary: Colors.white,
              surface: Colors.white,
              onSurface: Color(0xFF2D3748),
            ),
          ),
          child: child!,
        );
      },
    );

    if (picked != null) {
      setState(() {
        final formattedDate =
            '${picked.day.toString().padLeft(2, '0')}/${picked.month.toString().padLeft(2, '0')}/${picked.year}';

        if (isStartDate) {
          _startDateController.text = formattedDate;
          // Clear end date if it's before the new start date
          if (_endDateController.text.isNotEmpty) {
            try {
              // Parse DD/MM/YYYY format
              final dateParts = _endDateController.text.split('/');
              if (dateParts.length == 3) {
                final endDate = DateTime(
                  int.parse(dateParts[2]), // year
                  int.parse(dateParts[1]), // month
                  int.parse(dateParts[0]), // day
                );
                if (endDate.isBefore(picked)) {
                  _endDateController.text = '';
                }
              }
            } catch (e) {
              // If parsing fails, clear end date to be safe
              _endDateController.text = '';
            }
          }
        } else {
          _endDateController.text = formattedDate;
        }
      });
    }
  }

  void _createItinerary() async {
    HapticFeedback.mediumImpact();

    // Final validation before creating itinerary
    if (!_canProceedFromStep(_currentStep)) {
      _showValidationError();
      return;
    }

    // Generate itinerary ID first
    final itineraryId = ItineraryService.generateId();

    // Save image to persistent storage if selected
    String? persistentImagePath;
    if (_selectedImage != null) {
      persistentImagePath = await ImageStorageService.saveImage(
        _selectedImage!.path,
        itineraryId,
      );

      if (persistentImagePath == null) {
        // Show error if image saving failed
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'Failed to save cover photo. Creating itinerary without image.',
                style: GoogleFonts.instrumentSans(color: Colors.white),
              ),
              backgroundColor: Colors.orange,
              behavior: SnackBarBehavior.floating,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(10),
              ),
            ),
          );
        }
      }
    }

    // Collect all filled destinations
    final destinations = _destinationControllers
        .map((controller) => controller.text.trim())
        .where((text) => text.isNotEmpty)
        .toList();

    // Collect day-specific activities
    final totalDays = _getTotalDays();
    final daySpecificActivities = <int, Map<String, List<String>>>{};

    for (int day = 1; day <= totalDays; day++) {
      if (_daySpecificActivitiesControllers.containsKey(day)) {
        final dayActivities = <String, List<String>>{};

        for (final destination in destinations) {
          if (_daySpecificActivitiesControllers[day]!
              .containsKey(destination)) {
            final activities =
                _daySpecificActivitiesControllers[day]![destination]!
                    .map((controller) => controller.text.trim())
                    .where((text) => text.isNotEmpty)
                    .toList();
            if (activities.isNotEmpty) {
              dayActivities[destination] = activities;
            }
          }
        }

        if (dayActivities.isNotEmpty) {
          daySpecificActivities[day] = dayActivities;
        }
      }
    }

    // Create legacy dailyActivities for backward compatibility (combine all days)
    final dailyActivities = <String, List<String>>{};
    for (final destination in destinations) {
      final allActivities = <String>[];
      for (int day = 1; day <= totalDays; day++) {
        if (daySpecificActivities.containsKey(day) &&
            daySpecificActivities[day]!.containsKey(destination)) {
          allActivities.addAll(daySpecificActivities[day]![destination]!);
        }
      }
      if (allActivities.isNotEmpty) {
        dailyActivities[destination] = allActivities;
      }
    }

    // Create itinerary object
    final itinerary = Itinerary(
      id: itineraryId,
      title: _titleController.text.trim(),
      startDate: _startDateController.text.trim(),
      endDate: _endDateController.text.trim(),
      destinations: destinations,
      hasPhoto: persistentImagePath != null,
      imagePath: persistentImagePath,
      dailyActivities: dailyActivities,
      daySpecificActivities:
          daySpecificActivities.isNotEmpty ? daySpecificActivities : null,
      accommodation: _accommodationController.text.trim(),
      additionalNotes: _additionalNotesController.text.trim(),
      createdAt: DateTime.now(),
    );

    // Save to local storage
    final success = await ItineraryService.saveItinerary(itinerary);

    // Check if widget is still mounted before using context
    if (!mounted) return;

    if (success) {
      // Show success message
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'Itinerary "${_titleController.text}" created successfully with ${destinations.length} destination${destinations.length != 1 ? 's' : ''}!',
            style: GoogleFonts.instrumentSans(color: Colors.white),
          ),
          backgroundColor: const Color(0xFF0D76FF),
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
          ),
          duration: const Duration(seconds: 2),
        ),
      );

      // Navigate back with result to trigger refresh
      Navigator.of(context).pop(true);
    } else {
      // Show error message
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'Failed to save itinerary. Please try again.',
            style: GoogleFonts.instrumentSans(color: Colors.white),
          ),
          backgroundColor: Colors.red,
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
          ),
          duration: const Duration(seconds: 2),
        ),
      );
    }
  }
}
