import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:flutter/foundation.dart';

class SupabaseConfig {
  static const String url = 'https://ktdstluymbpqejmjkpsg.supabase.co';
  static const String anonKey =
      'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imt0ZHN0bHV5bWJwcWVqbWprcHNnIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDQ0NDU1ODksImV4cCI6MjA2MDAyMTU4OX0.o8yf7Dsow4pt-A8uekGPQbJI2A-2NKdAqkRISnsYqEY';

  static Future<void> initialize() async {
    try {
      if (kDebugMode) {
        print('Supabase Config: Initializing with URL: $url');
      }

      await Supabase.initialize(
        url: url,
        anonKey: anonKey,
        debug: kDebugMode,
        authOptions: const FlutterAuthClientOptions(
          authFlowType: AuthFlowType.pkce,
        ),
        realtimeClientOptions: const RealtimeClientOptions(
          logLevel: RealtimeLogLevel.info,
        ),
      );

      if (kDebugMode) {
        print('Supabase Config: Successfully initialized');
      }
    } catch (error) {
      if (kDebugMode) {
        print('Supabase Config: Initialization error: $error');
      }
      rethrow;
    }
  }

  static SupabaseClient get client => Supabase.instance.client;

  /// Test connection to Supabase
  static Future<bool> testConnection() async {
    try {
      if (kDebugMode) {
        print('Supabase Config: Testing connection...');
      }

      // Try a simple health check
      final response =
          await client.from('_supabase_health_check').select().limit(1);

      if (kDebugMode) {
        print('Supabase Config: Connection test successful');
      }
      return true;
    } catch (error) {
      if (kDebugMode) {
        print('Supabase Config: Connection test failed: $error');
      }
      return false;
    }
  }
}
