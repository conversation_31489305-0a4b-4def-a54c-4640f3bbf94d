import 'package:flutter/material.dart';
import '../services/auth_service.dart';
import '../screens/survey_screen.dart';

/// Utility class to handle survey completion guards throughout the app
class SurveyGuard {
  /// Check if the current user needs to complete the survey
  /// Returns true if user is authenticated but hasn't completed survey
  static Future<bool> needsSurveyCompletion() async {
    return await AuthService.needsSurveyCompletion();
  }

  /// Check if the current user has completed the survey
  /// Returns true if user has completed survey or is a guest user
  static Future<bool> hasSurveyCompleted() async {
    return await AuthService.hasSurveyCompleted();
  }

  /// Navigate to survey screen if user needs to complete it
  /// Returns true if navigation occurred, false if user can proceed
  static Future<bool> checkAndNavigateToSurvey(BuildContext context) async {
    final needsSurvey = await needsSurveyCompletion();

    if (needsSurvey && context.mounted) {
      Navigator.of(context).pushReplacement(
        PageRouteBuilder(
          pageBuilder: (context, animation, secondaryAnimation) =>
              const SurveyScreen(),
          transitionDuration: const Duration(milliseconds: 300),
          reverseTransitionDuration: const Duration(milliseconds: 300),
          transitionsBuilder: (context, animation, secondaryAnimation, child) {
            const begin = Offset(1.0, 0.0);
            const end = Offset.zero;
            const curve = Curves.easeInOut;

            var tween = Tween(begin: begin, end: end).chain(
              CurveTween(curve: curve),
            );

            return SlideTransition(
              position: animation.drive(tween),
              child: child,
            );
          },
        ),
      );
      return true;
    }

    return false;
  }

  /// Widget wrapper that checks survey completion before showing content
  /// If user needs to complete survey, shows survey screen instead
  static Widget guardedWidget({
    required Widget child,
    Widget? loadingWidget,
  }) {
    return FutureBuilder<bool>(
      future: needsSurveyCompletion(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return loadingWidget ??
              const Scaffold(
                backgroundColor: Color(0xFF0D76FF),
                body: Center(
                  child: CircularProgressIndicator(
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  ),
                ),
              );
        }

        final needsSurvey = snapshot.data ?? false;

        if (needsSurvey) {
          return const SurveyScreen();
        } else {
          return child;
        }
      },
    );
  }

  /// Check if user is a guest (anonymous) user
  /// Guest users don't need to complete surveys
  static bool isGuestUser() {
    return AuthService.getUserType() == 'guest';
  }

  /// Check if user is a registered user (not guest/anonymous)
  /// Only registered users need to complete surveys
  static bool isRegisteredUser() {
    return AuthService.isRegisteredUser;
  }

  /// Get user type for survey requirements
  /// Returns: 'unauthenticated', 'guest', or 'registered'
  static String getUserType() {
    return AuthService.getUserType();
  }

  /// Validate that a screen should be accessible
  /// Returns true if user can access the screen, false otherwise
  static Future<bool> canAccessScreen() async {
    final userType = getUserType();

    // Unauthenticated users should not access protected screens
    if (userType == 'unauthenticated') {
      return false;
    }

    // Guest users can access screens without survey
    if (userType == 'guest') {
      return true;
    }

    // Registered users must complete survey
    if (userType == 'registered') {
      return await hasSurveyCompleted();
    }

    return false;
  }

  /// Show a dialog explaining survey requirement
  static void showSurveyRequiredDialog(BuildContext context) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: const Color(0xFFF7F9FC),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          title: const Text('Complete Your Profile'),
          content: const Text(
            'To access this feature, please complete your travel preferences survey. This helps us personalize your experience.',
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                Navigator.of(context).pushReplacement(
                  MaterialPageRoute(
                    builder: (context) => const SurveyScreen(),
                  ),
                );
              },
              child: const Text('Complete Survey'),
            ),
          ],
        );
      },
    );
  }

  /// Middleware function to wrap route handlers with survey checks
  static Widget Function(BuildContext) guardRoute(
      Widget Function(BuildContext) builder) {
    return (BuildContext context) {
      return guardedWidget(
        child: builder(context),
      );
    };
  }
}
